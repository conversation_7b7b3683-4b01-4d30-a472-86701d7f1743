import { createRouter, createWebHashHistory } from 'vue-router'

const routes = [
  // 首页，项目介绍与引导
  {
    path: '/',
    name: 'Home',
    component: () => import('../views/Home.vue'),
  },
  // 登录页
  {
    path: '/login',
    name: 'Login',
    component: () => import('../views/Login.vue'),
  },
  // 注册页
  {
    path: '/register',
    name: 'Register',
    component: () => import('../views/Register.vue'),
  },
  // 用户面板
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('../views/Dashboard.vue'),
  },
  // 开箱页面
  {
    path: '/box',
    name: 'BoxOpening',
    component: () => import('../views/BoxOpening.vue'),
  },
  // 积分排行榜
  {
    path: '/ranking',
    name: 'Ranking',
    component: () => import('../views/Ranking.vue'),
  },
  // 5晋4第二天
  {
    path: '/second-day',
    name: 'SecondDay',
    component: () => import('../views/SecondDay.vue'),
  },
  // 规则讲解
  {
    path: '/rules',
    name: 'Rules',
    component: () => import('../views/Rules.vue'),
  },
  // 赛事日程
  {
    path: '/schedule',
    name: 'Schedule',
    component: () => import('../views/Schedule.vue'),
  },
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})

export default router 