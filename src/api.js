// 本地模拟用户数据
const users = [{ username: 'test', password: '123456' }]

export function login(username, password) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const user = users.find(u => u.username === username && u.password === password)
      if (user) {
        resolve({ code: 0, msg: '登录成功', user: { username } })
      } else {
        reject({ code: 1, msg: '用户名或密码错误' })
      }
    }, 500)
  })
}

export function register(username, password) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (users.find(u => u.username === username)) {
        reject({ code: 1, msg: '用户名已存在' })
      } else {
        users.push({ username, password })
        // 注册成功后直接登录
        resolve({ code: 0, msg: '注册成功', user: { username } })
      }
    }, 500)
  })
} 