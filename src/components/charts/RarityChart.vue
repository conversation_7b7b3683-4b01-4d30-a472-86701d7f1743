<template>
  <div class="rarity-chart-container">
    <h3 class="chart-title">{{ title }}</h3>
    <div class="chart-visualization">
      <!-- Basic chart placeholder - replace with actual chart implementation -->
      <div class="chart-bar-container">
        <div v-for="item in data" :key="item.name" class="chart-bar-wrapper">
          <div class="chart-bar" :style="{ height: `${item.value * 2}px`, backgroundColor: item.color }"></div>
          <div class="chart-label">
            <span class="label-name">{{ item.name }}</span>
            <span class="label-value">{{ item.value }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * Rarity Chart Component
 * @component
 * @description Displays a simple bar chart for rarity distribution
 */
export default {
  /**
   * Component name
   * @property {string} name - Component identifier
   */
  name: 'Rarity<PERSON><PERSON>',

  /**
   * Component props
   * @property {Object} props - Component properties
   */
  props: {
    /**
     * Chart title
     * @type {string}
     * @default 'Rarity Distribution'
     */
    title: {
      type: String,
      default: 'Rarity Distribution'
    },

    /**
     * Chart data array
     * @type {Array<Object>}
     * @property {string} name - Data series name
     * @property {number} value - Data series value
     * @property {string} color - Data series color
     * @required
     */
    data: {
      type: Array,
      required: true,
      validator: (value) => {
        return value.every(item => item.name && item.value !== undefined && item.color);
      }
    }
  }
};
</script>

<style scoped>
.rarity-chart-container {
  width: 100%;
  padding: 1rem;
}

.chart-title {
  color: #ffffff;
  margin-bottom: 1rem;
  text-align: center;
}

.chart-visualization {
  height: 300px;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  gap: 1.5rem;
  padding-bottom: 2rem;
  position: relative;
}

.chart-visualization::after {
  content: '';
  position: absolute;
  bottom: 1.5rem;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.2);
}

.chart-bar-container {
  display: flex;
  align-items: flex-end;
  gap: 1.5rem;
  width: 100%;
  justify-content: center;
}

.chart-bar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.chart-bar {
  width: 40px;
  border-radius: 4px 4px 0 0;
  transition: height 0.5s ease;
}

.chart-label {
  color: #ffffff;
  text-align: center;
}

.label-name {
  display: block;
  font-size: 0.9rem;
}

.label-value {
  font-weight: bold;
  font-size: 1.1rem;
}
</style>