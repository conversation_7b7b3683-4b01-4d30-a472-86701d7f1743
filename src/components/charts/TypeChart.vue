<template>
  <div class="type-chart-container">
    <h3 class="chart-title">{{ title }}</h3>
    <div class="chart-visualization">
      <!-- Basic pie chart placeholder - replace with actual chart implementation -->
      <div class="pie-chart">
        <div class="pie-slice"
             v-for="(item, index) in data"
             :key="item.name"
             :style="{ transform: `rotate(${getRotation(index)})`, '--slice-color': item.color }"
        ></div>
      </div>
      <div class="chart-legend">
        <div v-for="item in data" :key="item.name" class="legend-item">
          <div class="legend-color" :style="{ backgroundColor: item.color }"></div>
          <div class="legend-label">
            <span class="label-name">{{ item.name }}</span>
            <span class="label-value">({{ item.value }})</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
/**
 * Type Chart Component
 * @component
 * @description Displays a simple pie chart for item type distribution
 */
export default {
  /**
   * Component name
   * @property {string} name - Component identifier
   */
  name: 'TypeChart',

  /**
   * Component props
   * @property {Object} props - Component properties
   */
  props: {
    /**
     * Chart title
     * @type {string}
     * @default 'Item Type Distribution'
     */
    title: {
      type: String,
      default: 'Item Type Distribution'
    },

    /**
     * Chart data array
     * @type {Array<Object>}
     * @property {string} name - Data series name
     * @property {number} value - Data series value
     * @property {string} color - Data series color
     * @required
     */
    data: {
      type: Array,
      required: true,
      validator: (value) => {
        return value.every(item => item.name && item.value !== undefined && item.color);
      }
    }
  },

  /**
   * Component methods
   * @property {Object} methods - Component methods
   */
  methods: {
    /**
     * Calculate rotation for pie chart slices
     * @method getRotation
     * @param {number} index - Slice index
     * @returns {string} Rotation value in degrees
     */
    getRotation(index) {
      const total = this.data.reduce((sum, item) => sum + item.value, 0);
      let rotation = 0;
      for (let i = 0; i < index; i++) {
        rotation += (this.data[i].value / total) * 360;
      }
      return `${rotation}deg`;
    }
  }
};
</script>

<style scoped>
.type-chart-container {
  width: 100%;
  padding: 1rem;
}

.chart-title {
  color: #ffffff;
  margin-bottom: 1rem;
  text-align: center;
}

.chart-visualization {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 2rem;
}

.pie-chart {
  width: 200px;
  height: 200px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.pie-slice {
  position: absolute;
  width: 50%;
  height: 100%;
  transform-origin: right center;
  transition: transform 0.5s ease;
}

.pie-slice::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background-color: var(--slice-color);
}

.chart-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.8rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
}

.legend-label {
  color: #ffffff;
}

.label-name {
  margin-right: 0.5rem;
}

.label-value {
  color: rgba(255, 255, 255, 0.7);
}

@media (max-width: 768px) {
  .chart-visualization {
    flex-direction: column;
  }
}
</style>