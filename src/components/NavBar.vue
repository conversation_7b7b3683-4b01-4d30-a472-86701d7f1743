<template>
  <nav class="navbar">
    <!-- 军事风格logo区域 -->
    <!-- <router-link to="/" class="logo" style="text-decoration: none;">
      <img src="/Unknown-5.png" alt="PUBG Logo" class="game-logo">
      <span class="logo-text">BATTLEFIELD<span class="sub-text">抽卡系统</span></span>
    </router-link> -->
    
    <div class="nav-actions">
      <!-- 回到首页按钮 -->
      <router-link to="/" class="nav-btn military-btn home-btn" style="overflow:hidden">
        <i class="icon">🏠</i> 回到首页
      </router-link>

      <template v-if="user">
        <!-- 已登录用户信息 -->
        <div @click="logout"><router-link class="nav-btn military-btn exit-battlefield-btn"  style="overflow:hidden">
          <i class="icon">⚡</i> 退出战场
        </router-link>
      </div>
    </template>
      <template v-else>
        <!-- 未登录状态 -->
        <router-link to="/login" class="nav-btn military-btn pubg-nav-btn login-btn" style="overflow:hidden">
          <i class="icon">🔑</i> 用户登录
        </router-link>
      </template>
       <!-- 导航菜单 -->
       <router-link
         :to="currentRoute === '/rules' ? '/dashboard' : '/rules'"
         class="nav-btn military-btn pubg-nav-btn"
         style="overflow:hidden">
        <i class="icon">📋</i>
        {{ currentRoute === '/rules' ? '关闭' : '规则讲解' }}
      </router-link>

      <router-link
        :to="currentRoute === '/schedule' ? '/dashboard' : '/schedule'"
        class="nav-btn military-btn pubg-nav-btn"
        style="overflow:hidden">
        <i class="icon">📅</i>
        {{ currentRoute === '/schedule' ? '关闭' : '赛事日程' }}
      </router-link>
    </div>
    <div>
      <div class="user-profile">
        <div class="avatar-frame">
          <img :src="user.avatar || '/Unknown-5.png'" alt="User Avatar" class="user-avatar">
        </div>
        <span class="user-name">{{ user.username }}</span>
      </div>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'NavBar',
  data() {
    return {
      user: null,
      isScrolled: false // 新增滚动状态
    }
  },
  computed: {
    currentRoute() {
      return this.$route.path
    }
  },
  created() {
    this.loadUser()
    window.addEventListener('storage', this.loadUser)
    // 新增滚动监听
    window.addEventListener('scroll', this.handleScroll)
    this.handleScroll() // 初始化状态
  },
  beforeUnmount() {
    window.removeEventListener('storage', this.loadUser)
    // 移除滚动监听
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    loadUser() {
      const u = localStorage.getItem('user')
      this.user = u ? JSON.parse(u) : null
    },
    logout() {
      localStorage.removeItem('user')
      this.user = null
      this.$router.push('/')
    },
    // 新增滚动处理方法
    handleScroll() {
      this.isScrolled = window.scrollY > 10
    }
  },
  watch: {
    $route() {
      this.loadUser()
    }
  }
}
</script>

<style scoped>
/* 导航栏基础样式 - 军事主题改造 */
.navbar {
  position: fixed;
  z-index: 100;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 36px;
  top: 0;
  left: 0;
  box-sizing: border-box;
  /* 军事迷彩背景 */
  background-image: linear-gradient(rgba(22, 26, 30, 0.95), rgba(18, 20, 24, 0.95)), url('/bg_fixed2_1080p_001.ts');
  background-size: cover;
  background-blend-mode: overlay;
  /* 金属质感边框 */
  border-bottom: 2px solid;
  border-image: linear-gradient(90deg, transparent, #f5c242, transparent) 1;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

/* Logo区域样式优化 */
.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 导航操作区域 */
.nav-actions {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.game-logo {
  height: 42px;
  border-radius: 4px;
}

.logo-text {
  font-size: 1.4rem;
  font-weight: bold;
  color: #f5c242;
  letter-spacing: 1px;
  text-shadow: 0 0 5px rgba(245, 194, 66, 0.5);
}

.sub-text {
  font-size: 0.9rem;
  color: #e0e0e0;
  margin-left: 8px;
  text-shadow: none;
}

/* 用户资料区域 - 新增军事风格设计 */
.user-profile {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar-frame {
  position: relative;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid #f5c242;
  object-fit: cover;
  box-shadow: 0 0 8px rgba(245, 194, 66, 0.5);
}

.rank-badge {
  position: absolute;
  bottom: -5px;
  right: -5px;
  background-color: #5a4016;
  color: #f5c242;
  font-size: 0.6rem;
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid #f5c242;
}

.user-name {
  color: #f5c242;
  font-weight: bold;
  text-shadow: 0 0 3px rgba(245, 194, 66, 0.5);
}

/* 军事风格按钮 - PUBG风格 */
.military-btn {
  padding: 6px 12px;
  margin-left: 8px;
  border-radius: 4px;
  border: 1.5px solid #5a4016;
  background: linear-gradient(to bottom, #3a3a3a 0%, #2a2a2a 100%);
  color: #f5c242;
  font-weight: 500;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  flex-shrink: 0;
}

/* 滚动状态样式变化 */
.navbar.scrolled {
  padding: 8px 36px;
  background-image: linear-gradient(rgba(22, 26, 30, 0.98), rgba(18, 20, 24, 0.98)), url('/bg_fixed2_1080p_001.ts');
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

.military-btn:hover {
  background: linear-gradient(to bottom, #f5c242 0%, #d4a024 100%);
  color: #1a1a1a;
  border-color: #f5c242;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(245, 194, 66, 0.3);
}

.military-btn.primary {
  background: linear-gradient(to bottom, #5a4016 0%, #423010 100%);
  border-color: #f5c242;
  margin-left: 12px;
  box-shadow: 0 2px 8px rgba(245, 194, 66, 0.3);
}

.military-btn.primary:hover {
  background: linear-gradient(to bottom, #f5c242 0%, #d4a024 100%);
  color: #1a1a1a;
  border-color: #f5c242;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 194, 66, 0.5);
}

/* 按钮图标样式 */
.military-btn .icon {
  font-size: 1rem;
  margin-right: 4px;
  filter: drop-shadow(0 0 2px rgba(245, 194, 66, 0.5));
}

/* 回到首页按钮样式 */
.home-btn {
  background: linear-gradient(to bottom, #2d5a2d 0%, #1a3a1a 100%);
  border-color: #4a8a4a;
  color: #90ee90;
}

.home-btn:hover {
  background: linear-gradient(to bottom, #4a8a4a 0%, #2d5a2d 100%);
  color: #ffffff;
  border-color: #90ee90;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(144, 238, 144, 0.3);
}

/* 退出战场按钮 - PUBG危险风格 */
.exit-battlefield-btn {
  position: relative;
  padding: 6px 12px !important;
  box-sizing: border-box !important;
  height: auto !important;
  background: linear-gradient(135deg,
    rgba(139, 69, 19, 0.9) 0%,
    rgba(160, 82, 45, 0.9) 25%,
    rgba(139, 69, 19, 0.9) 50%,
    rgba(101, 67, 33, 0.9) 100%) !important;
  border: 1.5px solid #cd853f !important;
  color: #ffd700 !important;
  box-shadow:
    0 0 10px rgba(205, 133, 63, 0.5),
    inset 0 1px 0 rgba(255, 215, 0, 0.3),
    inset 0 -1px 0 rgba(139, 69, 19, 0.8);
  text-shadow:
    0 0 5px rgba(255, 215, 0, 0.8),
    0 1px 2px rgba(0, 0, 0, 0.8);
  font-weight: 600;
  letter-spacing: 0.5px;
}

.exit-battlefield-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 215, 0, 0.3),
    transparent);
  transition: left 0.6s ease;
}

.exit-battlefield-btn:hover::before {
  left: 100%;
}

.exit-battlefield-btn:hover {
  background: linear-gradient(135deg,
    rgba(178, 34, 34, 0.95) 0%,
    rgba(220, 20, 60, 0.95) 25%,
    rgba(178, 34, 34, 0.95) 50%,
    rgba(139, 0, 0, 0.95) 100%) !important;
  border-color: #ff6347 !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow:
    0 0 15px rgba(255, 99, 71, 0.7),
    0 4px 12px rgba(178, 34, 34, 0.6),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow:
    0 0 8px rgba(255, 255, 255, 0.9),
    0 1px 3px rgba(0, 0, 0, 0.9);
}

.exit-battlefield-btn .icon {
  filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.9));
  animation: pulse 2s infinite;
}

.exit-battlefield-btn:hover .icon {
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 1));
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* PUBG导航按钮通用样式 */
.pubg-nav-btn {
  position: relative;
  padding: 6px 12px !important;
  box-sizing: border-box !important;
  height: auto !important;
  background: linear-gradient(135deg,
    rgba(139, 69, 19, 0.9) 0%,
    rgba(160, 82, 45, 0.9) 25%,
    rgba(139, 69, 19, 0.9) 50%,
    rgba(101, 67, 33, 0.9) 100%) !important;
  border: 1.5px solid #cd853f !important;
  color: #ffd700 !important;
  box-shadow:
    0 0 8px rgba(205, 133, 63, 0.4),
    inset 0 1px 0 rgba(255, 215, 0, 0.2),
    inset 0 -1px 0 rgba(139, 69, 19, 0.6);
  text-shadow:
    0 0 3px rgba(255, 215, 0, 0.6),
    0 1px 2px rgba(0, 0, 0, 0.8);
  font-weight: 600;
  letter-spacing: 0.3px;
}

.pubg-nav-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgba(255, 215, 0, 0.25),
    transparent);
  transition: left 0.5s ease;
}

.pubg-nav-btn:hover::before {
  left: 100%;
}

.pubg-nav-btn:hover {
  background: linear-gradient(135deg,
    rgba(184, 134, 11, 0.95) 0%,
    rgba(218, 165, 32, 0.95) 25%,
    rgba(184, 134, 11, 0.95) 50%,
    rgba(139, 101, 8, 0.95) 100%) !important;
  border-color: #ffd700 !important;
  color: #ffffff !important;
  transform: translateY(-1px);
  box-shadow:
    0 0 12px rgba(255, 215, 0, 0.6),
    0 4px 10px rgba(184, 134, 11, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  text-shadow:
    0 0 6px rgba(255, 255, 255, 0.8),
    0 1px 3px rgba(0, 0, 0, 0.9);
}

.pubg-nav-btn .icon {
  filter: drop-shadow(0 0 3px rgba(255, 215, 0, 0.7));
  transition: all 0.3s ease;
}

.pubg-nav-btn:hover .icon {
  filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.9));
  transform: scale(1.05);
}

/* 登录按钮特殊样式 */
.login-btn {
  background: linear-gradient(135deg,
    rgba(25, 25, 112, 0.9) 0%,
    rgba(65, 105, 225, 0.9) 25%,
    rgba(25, 25, 112, 0.9) 50%,
    rgba(0, 0, 139, 0.9) 100%) !important;
  border-color: #4169e1 !important;
  color: #87ceeb !important;
}

.login-btn:hover {
  background: linear-gradient(135deg,
    rgba(65, 105, 225, 0.95) 0%,
    rgba(100, 149, 237, 0.95) 25%,
    rgba(65, 105, 225, 0.95) 50%,
    rgba(30, 144, 255, 0.95) 100%) !important;
  border-color: #87ceeb !important;
  color: #ffffff !important;
  box-shadow:
    0 0 12px rgba(135, 206, 235, 0.6),
    0 4px 10px rgba(65, 105, 225, 0.5),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 响应式调整 - 适配移动设备 */
@media (max-width: 768px) {
  .navbar {
    padding: 8px 16px;
  }

  .logo-text {
    font-size: 1.1rem;
  }

  .military-btn {
    padding: 4px 8px;
    font-size: 0.75rem;
    margin-left: 6px;
  }

  .user-name {
    display: none;
  }

  .nav-actions {
    gap: 4px;
  }
}

/* 更小屏幕优化 */
@media (max-width: 480px) {
  .military-btn {
    padding: 4px 6px;
    font-size: 0.7rem;
    margin-left: 4px;
  }

  .military-btn .icon {
    font-size: 0.8rem;
    margin-right: 2px;
  }

  .exit-battlefield-btn,
  .pubg-nav-btn {
    padding: 4px 8px;
    font-size: 0.7rem;
  }

  .exit-battlefield-btn .icon,
  .pubg-nav-btn .icon {
    font-size: 0.9rem;
  }
}
</style>