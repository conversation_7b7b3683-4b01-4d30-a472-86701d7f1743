<template>
  <div class="card-flip" :class="{ flipped: isFlipped }" @click="handleClick">
    <div class="card-flip-inner">
      <div class="card-flip-front" :style="{ backgroundImage: `url(${backImg})` }"></div>
      <div class="card-flip-back" :style="{ borderColor: rarityColor }">
        <img :src="img" class="card-img" alt="卡牌正面" />
        <div class="card-info">
          <div class="card-name">{{ name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CardFlip',
  props: {
    isFlipped: { type: Boolean, default: false },
    img: { type: String, default: '' },
    name: { type: String, default: '' },
    type: { type: String, default: '' },
    rarity: { type: String, default: '' },
    rarityColor: { type: String, default: '#607D8B' },
    backImg: { type: String, default: '/kpkp.png' },
    clickable: { type: Boolean, default: false },
  },
  emits: ['flip'],
  methods: {
    handleClick() {
      if (this.clickable) this.$emit('flip')
    }
  }
}
</script>

<style scoped>
.card-flip {
  perspective: 1200px;
  width: 100%;
  max-width: 220px;
  min-width: 120px;
  aspect-ratio: 3/4;
  margin: 0 auto;
  cursor: pointer;
  user-select: none;
}
.card-flip-inner {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 133.33%; /* 3:4比例 */
  transition: transform 0.7s cubic-bezier(.4,2,.6,1);
  transform-style: preserve-3d;
}
.card-flip.flipped .card-flip-inner {
  transform: rotateY(180deg);
}
.card-flip-front {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0; left: 0;
  backface-visibility: hidden;
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 4px 16px #0008;
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  z-index: 2;
  margin: 0;
  padding: 0;
  background-color: transparent;
}
.card-flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0; left: 0;
  backface-visibility: hidden;
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0 4px 16px #0008;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #23262e;
  border: 2.5px solid #607D8B;
  transform: rotateY(180deg);
  z-index: 3;
}
.card-img {
  width: 80%;
  height: 60%;
  object-fit: contain;
  margin: 0 auto;
  margin-top: 12%;
  border-radius: 8px;
  background: #181a20;
}
.card-info {
  margin-top: 10px;
  text-align: center;
  margin-bottom: 12px;
}
.card-name {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
}
.card-type {
  color: #a9b1bb;
  font-size: 0.9rem;
  margin-bottom: 8px;
}
.rarity-tag {
  color: #fff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-block;
}
@media (max-width: 600px) {
  .card-flip { max-width: 120px; min-width: 60px; }
  .card-img { width: 90%; height: 60%; }
}
</style> 