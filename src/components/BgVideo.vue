<template>
  <div class="video-bg">
    <video ref="bgVideo" class="bg-video" :poster="poster" autoplay muted playsinline @ended="onEnded" @error="onError"></video>
    <div class="video-mask"></div>
  </div>
</template>

<script>
import Hls from 'hls.js'
export default {
  name: 'BgVideo',
  props: {
    src: {
      type: String,
      default: '/bg_fixed2_1080p.m3u8',
    },
    srcList: {
      type: Array,
      default: () => [],
    },
    poster: {
      type: String,
      default: '/first_frame.jpg',
    },
  },
  data() {
    return {
      currentIdx: 0,
      hls: null,
      preloaded: {}, // 记录每个视频是否已加载
      switching: false,
    }
  },
  computed: {
    videoList() {
      return this.srcList && this.srcList.length > 0 ? this.srcList : [this.src]
    }
  },
  mounted() {
    this.initVideo(this.currentIdx)
    this.preloadNext()
  },
  methods: {
    initVideo(idx) {
      const video = this.$refs.bgVideo
      const src = this.videoList[idx]
      if (this.hls) {
        this.hls.destroy()
        this.hls = null
      }
      if (video.canPlayType('application/vnd.apple.mpegurl')) {
        video.src = src
        video.load()
        video.play()
        this.preloaded[idx] = true
      } else if (Hls.isSupported()) {
        const hls = new Hls()
        hls.loadSource(src)
        hls.attachMedia(video)
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          this.preloaded[idx] = true
        })
        hls.on(Hls.Events.ERROR, (event, data) => {
          if (data.fatal) this.onError()
        })
        this.hls = hls
      }
    },
    preloadNext() {
      // 预加载下一个视频
      const nextIdx = (this.currentIdx + 1) % this.videoList.length
      if (this.preloaded[nextIdx]) return
      const src = this.videoList[nextIdx]
      // 只预加载，不attach到video
      if (Hls.isSupported()) {
        const hls = new Hls()
        hls.loadSource(src)
        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          this.preloaded[nextIdx] = true
          hls.destroy()
        })
        hls.on(Hls.Events.ERROR, () => {
          hls.destroy()
        })
      } else {
        // 浏览器原生支持m3u8
        const testVideo = document.createElement('video')
        testVideo.src = src
        testVideo.preload = 'auto'
        testVideo.oncanplay = () => {
          this.preloaded[nextIdx] = true
        }
      }
    },
    onEnded() {
      this.switchToNext()
    },
    onError() {
      // 播放出错时也尝试切换
      this.switchToNext()
    },
    switchToNext() {
      if (this.switching) return
      this.switching = true
      const nextIdx = (this.currentIdx + 1) % this.videoList.length
      if (this.preloaded[nextIdx]) {
        this.currentIdx = nextIdx
        this.initVideo(this.currentIdx)
        this.preloadNext()
        this.switching = false
      } else {
        // 下一个未加载好，回到第一个
        this.currentIdx = 0
        this.initVideo(0)
        this.preloadNext()
        // 继续检测下一个是否加载好
        setTimeout(() => {
          this.switching = false
          this.switchToNext()
        }, 2000)
      }
    },
  },
  watch: {
    srcList() {
      this.currentIdx = 0
      this.preloaded = {}
      this.initVideo(0)
      this.preloadNext()
    }
  }
}
</script>

<style scoped>
.video-bg {
  position: fixed;
  inset: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  top: 0; left: 0;
margin: 0;
padding: 0;
box-sizing: border-box;
  overflow: hidden;
}
.bg-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.video-mask {
  position: absolute;
  inset: 0;
  background: rgba(0,0,0,0.45);
  z-index: 1;
}
</style> 