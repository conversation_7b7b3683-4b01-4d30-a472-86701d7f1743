<template>
  <div id="app">
    <BgVideo />
    <NavBar />
    <!-- 页面内容 -->
      <router-view />
  </div>
</template>

<script>
import BgVideo from './components/BgVideo.vue'
import NavBar from './components/NavBar.vue'
export default {
  name: 'App',
  components: { BgVideo, NavBar },
}
</script>

<style>
html, body, #app {
  margin: 0;
  padding: 0;
}
body {
  font-family: 'PingFang SC', 'Microsoft YaHei', Arial, sans-serif;
  background: #181a20;
  color: #fff;
}

#app {
  display: flex;
  flex-direction: column;
}
</style>