<template>
  <div class="secondday-bg">

    <div class="secondday-container">
      <!-- 游戏风格标题栏 -->
      <div class="secondday-header">
        <div class="header-title">
          <img src="/Unknown-5.png" alt="PUBG Logo" class="header-logo">
          <h1>5晋4第二天</h1>
        </div>
        <div class="header-divider"></div>
      </div>

      <div class="secondday-content">
        <div class="pane-header">
          <h2 class="pane-title">晋级战场</h2>
          <div class="military-subtitle">强者晋级，弱者淘汰</div>
        </div>
        
        <div class="secondday-sections">
          <div class="section-card">
            <h3 class="section-title">晋级规则</h3>
            <div class="section-content">
              <ul>
                <li>每组5人，比赛后晋级前4名。</li>
                <li>晋级名单根据积分排名自动生成。</li>
                <li>淘汰者将无法参加后续比赛。</li>
              </ul>
            </div>
          </div>

          <div class="section-card">
            <h3 class="section-title">分组及晋级名单</h3>
            <div class="section-content">
              <div class="group-table-container">
                <table class="group-table">
                  <thead>
                    <tr>
                      <th>分组</th>
                      <th>战士昵称</th>
                      <th>战场积分</th>
                      <th>晋级状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td rowspan="5" class="group-name">A组</td>
                      <td>小明</td>
                      <td>120</td>
                      <td><span class="status-advance">晋级</span></td>
                    </tr>
                    <tr>
                      <td>小红</td>
                      <td>110</td>
                      <td><span class="status-advance">晋级</span></td>
                    </tr>
                    <tr>
                      <td>小刚</td>
                      <td>105</td>
                      <td><span class="status-advance">晋级</span></td>
                    </tr>
                    <tr>
                      <td>小美</td>
                      <td>98</td>
                      <td><span class="status-advance">晋级</span></td>
                    </tr>
                    <tr>
                      <td>小李</td>
                      <td>80</td>
                      <td><span class="status-eliminate">淘汰</span></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SecondDay'
}
</script>

<style scoped>
/* 基础样式 */
.secondday-bg {
  display: flex;
  justify-content: center;
  padding: 20px 24px;
  padding-top: 100px;
  box-sizing: border-box;
  background-image: url('/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 1;
}

.military-border {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5c242, transparent);
  left: 20px;
  right: 20px;
}

.military-border.top {
  top: 20px;
}

.military-border.bottom {
  bottom: 20px;
}

.corner-decoration {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #f5c242;
}

.corner-decoration.top-left {
  top: 20px;
  left: 20px;
  border-right: none;
  border-bottom: none;
}

.corner-decoration.top-right {
  top: 20px;
  right: 20px;
  border-left: none;
  border-bottom: none;
}

.corner-decoration.bottom-left {
  bottom: 20px;
  left: 20px;
  border-right: none;
  border-top: none;
}

.corner-decoration.bottom-right {
  bottom: 20px;
  right: 20px;
  border-left: none;
  border-top: none;
}

/* 主容器 */
.secondday-container {
  width: 100%;
  max-width: 1200px;
  background: rgba(18, 22, 30, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 2px solid rgba(245, 194, 66, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5),
              inset 0 0 20px rgba(245, 194, 66, 0.1);
  position: relative;
  z-index: 2;
}

/* 头部标题 */
.secondday-header {
  padding: 20px 32px 0;
  text-align: center;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.header-logo {
  height: 40px;
  filter: drop-shadow(0 0 8px rgba(245, 194, 66, 0.6));
}

.header-title h1 {
  font-family: 'Arial Black', sans-serif;
  font-size: 1.8rem;
  color: #f5c242;
  text-shadow: 0 0 8px rgba(245, 194, 66, 0.6),
               0 2px 4px rgba(0, 0, 0, 0.8);
  margin: 0;
}

.header-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5c242, transparent);
  margin: 0 auto;
  width: 200px;
}

/* 内容区域 */
.secondday-content {
  padding: 24px 32px 32px;
}

.pane-header {
  text-align: center;
  margin-bottom: 30px;
}

.pane-title {
  font-family: 'Arial Black', sans-serif;
  font-size: 1.6rem;
  font-weight: 600;
  color: #f5c242;
  text-shadow: 0 0 8px rgba(245, 194, 66, 0.6);
  margin: 0 0 8px 0;
}

.military-subtitle {
  color: #b8860b;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 内容卡片 */
.secondday-sections {
  display: grid;
  gap: 24px;
}

.section-card {
  background: rgba(33, 36, 40, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(245, 194, 66, 0.2);
  overflow: hidden;
  transition: all 0.3s;
}

.section-card:hover {
  border-color: rgba(245, 194, 66, 0.4);
  box-shadow: 0 4px 12px rgba(245, 194, 66, 0.1);
}

.section-title {
  color: #f5c242;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
  padding: 20px 24px 16px;
  background: rgba(245, 194, 66, 0.1);
  border-bottom: 1px solid rgba(245, 194, 66, 0.2);
}

.section-content {
  padding: 20px 24px;
}

.section-content ul {
  color: #fffbe6;
  margin: 0;
  padding-left: 20px;
}

.section-content li {
  margin-bottom: 8px;
  line-height: 1.6;
}

/* 分组表格 */
.group-table-container {
  overflow-x: auto;
}

.group-table {
  width: 100%;
  border-collapse: collapse;
  color: #b8860b;
}

.group-table th, .group-table td {
  padding: 12px 16px;
  text-align: center;
  border-bottom: 1px solid rgba(245, 194, 66, 0.2);
}

.group-table th {
  color: #f5c242;
  font-size: 0.95rem;
  font-weight: 600;
  background: rgba(245, 194, 66, 0.1);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.group-table tbody tr:last-child td {
  border-bottom: none;
}

.group-table tbody tr:hover {
  background: rgba(245, 194, 66, 0.05);
}

.group-name {
  background: rgba(245, 194, 66, 0.15);
  color: #f5c242;
  font-weight: bold;
  font-size: 1.1rem;
  text-align: center;
  vertical-align: middle;
}

.status-advance {
  background: linear-gradient(45deg, #4CAF50, #66BB6A);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status-eliminate {
  background: linear-gradient(45deg, #f44336, #ef5350);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.85rem;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .secondday-bg { 
    padding: 10px; 
  }
  
  .secondday-content { 
    padding: 20px 16px; 
  }
  
  .section-content {
    padding: 16px;
  }
  
  .group-table th, .group-table td {
    padding: 8px 12px;
    font-size: 0.85rem;
  }
  
  .header-title h1 {
    font-size: 1.4rem;
  }
  
  .header-logo {
    height: 30px;
  }
}
</style> 