<template>
  <div class="home-container">
    <!-- <div class="header">
      <div class="logo">PUBG 抽卡系统</div>
      <div class="auth-buttons">
        <button class="auth-btn login">登录</button>
        <button class="auth-btn register">注册</button>
      </div>
    </div> -->
    
    <div class="main-content">
      <div class="center-content">
        <!-- 开始抽箱按钮 -->
        <div class="start-button-wrapper" @click="goToDrawPage">
          <img src="/anniu.png" alt="按钮" class="button-bg" />
          <span class="button-text">开始抽箱</span>
        </div>

        <!-- 5晋4第二天按钮 -->
        <div class="start-button-wrapper" @click="goToSecondDayPage">
          <img src="/anniu.png" alt="按钮" class="button-bg" />
          <span class="button-text">5晋4第二天</span>
        </div>

        <!-- 积分排行榜按钮 -->
        <div class="start-button-wrapper" @click="goToRankingPage">
          <img src="/anniu.png" alt="按钮" class="button-bg" />
          <span class="button-text">积分排行榜</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Home',
  methods: {
    goToDrawPage() {
      this.$router.push('/dashboard');
    },
    goToSecondDayPage() {
      this.$router.push('/second-day');
    },
    goToRankingPage() {
      this.$router.push('/ranking');
    }
  }
}
</script>

<style scoped>
.home-container {
  height: 100vh;
  width: 100%;
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
}

.logo {
  color: #00aeff;
  font-size: 24px;
  font-weight: bold;
}

.auth-buttons {
  display: flex;
  gap: 15px;
}

.auth-btn {
  background: transparent;
  border: 1px solid #00aeff;
  color: #00aeff;
  padding: 5px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.auth-btn:hover {
  background: rgba(0, 174, 255, 0.1);
}

.main-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.center-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: -50px; /* 调整按钮位置 */
  gap: 20px; /* 添加按钮间距 */
}

.start-button-wrapper {
  position: relative;
  cursor: pointer;
  width: 300px; /* 减小按钮宽度 */
  max-width: 70%; /* 响应式调整 */
}

.button-bg {
  width: 100%;
  height: auto;
  display: block;
}

.button-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 1.2rem; /* 减小字体大小防止换行 */
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  white-space: nowrap; /* 强制文本不换行 */
}
</style>