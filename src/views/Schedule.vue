<template>
  <div class="schedule-container">
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="title-icon">📅</i>
            赛事日程
          </h1>
          <p class="page-subtitle">PUBG抽卡系统活动与赛事安排</p>
        </div>
        <div class="current-time">
          <div class="time-display">
            <span class="time-label">当前时间</span>
            <span class="time-value">{{ currentTime }}</span>
          </div>
        </div>
      </div>
    </div>

    <div class="schedule-content">
      <div class="schedule-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="section-icon">🏆</i>
            赛事日程
          </h2>
          <div class="week-nav">
            <button class="week-btn" @click="changeWeek(-1)">
              <i>◀</i> 上周
            </button>
            <span class="current-week">第{{ currentWeek }}周</span>
            <button class="week-btn" @click="changeWeek(1)">
              下周 <i>▶</i>
            </button>
          </div>
        </div>

        <div class="events-grid">
          <div v-for="event in weeklyEvents" :key="event.id" class="event-card" :class="event.status">
            <div class="event-header">
              <div class="event-type" :class="event.type">{{ event.typeName }}</div>
              <div class="event-status" :class="event.status">{{ event.statusText }}</div>
            </div>
            <h3 class="event-title">{{ event.title }}</h3>
            <p class="event-description">{{ event.description }}</p>
            <div class="event-details">
              <div class="event-time">
                <i class="detail-icon">🕐</i>
                <span>{{ event.startTime }} - {{ event.endTime }}</span>
              </div>
              <div class="event-reward">
                <i class="detail-icon">🎁</i>
                <span>{{ event.reward }}</span>
              </div>
            </div>

            <!-- 分组与轮次信息 -->
            <div class="tournament-info" v-if="event.tournamentInfo">
              <h4 class="info-title">分组与轮次</h4>
              <div class="info-grid">
                <div class="info-item">
                  <span class="info-label">当前分组:</span>
                  <span class="info-value">{{ event.tournamentInfo.currentGroup }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">当前轮次:</span>
                  <span class="info-value">{{ event.tournamentInfo.currentRound }}</span>
                </div>
                <div class="info-item">
                  <span class="info-label">群组:</span>
                  <span class="info-value">{{ event.tournamentInfo.groups }}</span>
                </div>
              </div>
            </div>

            <!-- 群组示例 -->
            <div class="group-examples" v-if="event.groupExamples">
              <h4 class="info-title">群组示例</h4>
              <div class="group-list">
                <div v-for="group in event.groupExamples" :key="group.name" class="group-item">
                  <span class="group-name">{{ group.name }}:</span>
                  <span class="group-desc">{{ group.description }}</span>
                </div>
              </div>
            </div>

            <!-- 当前组陪玩名单 -->
            <div class="player-list" v-if="event.playerList">
              <h4 class="info-title">当前组陪玩名单</h4>
              <div class="players">
                <div v-for="player in event.playerList" :key="player.id" class="player-item">
                  <span class="player-number">{{ player.number }}号陪玩:</span>
                  <span class="player-name">{{ player.name }}</span>
                </div>
                <div class="player-note">
                  (名单仅为示例，实际数据由后台提供)
                </div>
              </div>
            </div>

            <div class="event-progress" v-if="event.progress !== undefined">
              <div class="progress-label">赛事进度</div>
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: event.progress + '%' }"></div>
              </div>
              <div class="progress-text">{{ event.progress }}%</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Schedule',
  data() {
    return {
      currentTime: '',
      currentWeek: 42,
      weeklyEvents: [
        {
          id: 1,
          type: 'tournament',
          typeName: '锦标赛',
          status: 'active',
          statusText: '进行中',
          title: 'PUBG春季锦标赛',
          description: '全球顶级战队齐聚，争夺春季冠军荣誉',
          startTime: '03-15 19:00',
          endTime: '03-17 22:00',
          reward: '冠军皮肤套装',
          progress: 45,
          tournamentInfo: {
            currentGroup: 'A组',
            currentRound: '第1轮',
            groups: 'abcd（共4个群）'
          },
          groupExamples: [
            { name: '群A', description: '麒麟座96进48' },
            { name: '群B', description: '天狼组96进48' },
            { name: '群C', description: '猎鹰组96进48' },
            { name: '群D', description: '猛虎组96进48' }
          ],
          playerList: [
            { id: 1, number: '1', name: '小明' },
            { id: 2, number: '2', name: '小红' },
            { id: 3, number: '3', name: '小刚' },
            { id: 4, number: '4', name: '小美' }
          ]
        },
        {
          id: 2,
          type: 'weekly',
          typeName: '周赛',
          status: 'upcoming',
          statusText: '即将开始',
          title: '周末精英赛',
          description: '每周末举行的精英玩家对抗赛',
          startTime: '03-23 20:00',
          endTime: '03-24 23:00',
          reward: '精英徽章 + 奖金',
          progress: 0,
          tournamentInfo: {
            currentGroup: 'B组',
            currentRound: '预选赛',
            groups: 'xy（共2个群）'
          },
          groupExamples: [
            { name: '群X', description: '精英组32进16' },
            { name: '群Y', description: '新星组32进16' }
          ],
          playerList: [
            { id: 1, number: '1', name: '战神' },
            { id: 2, number: '2', name: '影刃' },
            { id: 3, number: '3', name: '风暴' },
            { id: 4, number: '4', name: '雷电' }
          ]
        },
        {
          id: 3,
          type: 'special',
          typeName: '特殊赛事',
          status: 'ended',
          statusText: '已结束',
          title: '新年邀请赛',
          description: '新年特别邀请赛，明星选手参与',
          startTime: '01-01 15:00',
          endTime: '01-03 21:00',
          reward: '限定称号',
          progress: 100,
          tournamentInfo: {
            currentGroup: '决赛组',
            currentRound: '总决赛',
            groups: 'final（1个群）'
          },
          groupExamples: [
            { name: '决赛群', description: '明星邀请赛8进4' }
          ],
          playerList: [
            { id: 1, number: '1', name: '传奇' },
            { id: 2, number: '2', name: '王者' },
            { id: 3, number: '3', name: '至尊' },
            { id: 4, number: '4', name: '无敌' }
          ]
        }
      ]
    }
  },
  mounted() {
    this.updateTime()
    setInterval(this.updateTime, 1000)
  },
  methods: {
    updateTime() {
      const now = new Date()
      this.currentTime = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    changeWeek(direction) {
      this.currentWeek += direction
    }
  }
}
</script>

<style scoped>
.schedule-container {
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(20, 25, 35, 0.95) 0%,
    rgba(35, 40, 50, 0.95) 50%,
    rgba(20, 25, 35, 0.95) 100%),
    url('/bg_fixed2_1080p_001.ts');
  background-size: cover;
  background-attachment: fixed;
  padding-top: 80px;
}

.page-header {
  padding: 40px 0;
  text-align: center;
  border-bottom: 1px solid rgba(255, 165, 0, 0.3);
  margin-bottom: 30px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  font-size: 2rem;
  color: #ffa500;
  margin-bottom: 8px;
  text-shadow:
    0 0 8px rgba(255, 165, 0, 0.8),
    0 0 16px rgba(255, 165, 0, 0.4);
  font-weight: 800;
}

.title-icon {
  margin-right: 10px;
  font-size: 2.2rem;
  filter: drop-shadow(0 0 8px rgba(255, 165, 0, 0.8));
}

.page-subtitle {
  font-size: 1rem;
  color: #cccccc;
  margin: 0;
  letter-spacing: 1px;
}

.current-time {
  text-align: right;
}

.time-label {
  display: block;
  color: #888;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.time-value {
  color: #ffa500;
  font-size: 1.1rem;
  font-weight: bold;
  font-family: 'Courier New', monospace;
}



.schedule-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 60px;
}

.schedule-section {
  margin-bottom: 60px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.5rem;
  color: #ffa500;
  display: flex;
  align-items: center;
  margin: 0;
  text-shadow: 0 0 4px rgba(255, 165, 0, 0.6);
}

.section-icon {
  margin-right: 8px;
  font-size: 1.6rem;
  filter: drop-shadow(0 0 4px rgba(255, 165, 0, 0.8));
}

.week-nav {
  display: flex;
  align-items: center;
  gap: 15px;
}

.week-btn {
  background: linear-gradient(135deg,
    rgba(40, 45, 55, 0.9),
    rgba(30, 35, 45, 0.9));
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 8px;
  padding: 8px 15px;
  color: #ffa500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.week-btn:hover {
  border-color: #ffa500;
  background: linear-gradient(135deg,
    rgba(255, 165, 0, 0.2),
    rgba(255, 140, 0, 0.1));
}

.current-week {
  color: #ffa500;
  font-weight: bold;
  font-size: 1.1rem;
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.event-card {
  background: linear-gradient(135deg,
    rgba(40, 45, 55, 0.9),
    rgba(30, 35, 45, 0.9));
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 12px;
  padding: 25px;
  transition: all 0.3s ease;
}

.event-card:hover {
  transform: translateY(-5px);
  border-color: #ffa500;
  box-shadow: 0 12px 35px rgba(255, 165, 0, 0.2);
}

.event-card.active {
  border-color: #00ff00;
  box-shadow: 0 0 20px rgba(0, 255, 0, 0.3);
}

.event-card.ended {
  opacity: 0.6;
  border-color: #666;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.event-type {
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
}

.event-type.tournament {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: #fff;
}

.event-type.weekly {
  background: linear-gradient(135deg, #4834d4, #686de0);
  color: #fff;
}

.event-type.special {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
  color: #000;
}

.event-status {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
}

.event-status.active {
  background: #00ff00;
  color: #000;
}

.event-status.upcoming {
  background: #ffa500;
  color: #000;
}

.event-status.ended {
  background: #666;
  color: #fff;
}

.event-title {
  color: #ffa500;
  font-size: 1.3rem;
  margin: 0 0 10px 0;
  font-weight: 700;
}

.event-description {
  color: #e0e0e0;
  line-height: 1.6;
  margin-bottom: 15px;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 15px;
}

.event-time,
.event-reward {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ccc;
  font-size: 0.9rem;
}

.detail-icon {
  color: #ffa500;
  filter: drop-shadow(0 0 3px rgba(255, 165, 0, 0.6));
}

.event-progress {
  margin-top: 15px;
}

.progress-label {
  color: #ffa500;
  font-size: 0.9rem;
  margin-bottom: 8px;
  font-weight: bold;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
  margin-bottom: 5px;
}

.progress-fill {
  background: linear-gradient(90deg, #ffa500, #ff8c00);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.progress-text {
  color: #ffa500;
  font-size: 0.8rem;
  font-weight: bold;
}

.tournament-info,
.group-examples,
.player-list {
  margin-top: 20px;
  padding: 15px;
  background: rgba(20, 25, 35, 0.6);
  border-radius: 8px;
  border: 1px solid rgba(255, 165, 0, 0.2);
}

.info-title {
  color: #ffa500;
  font-size: 1.1rem;
  margin: 0 0 12px 0;
  font-weight: 700;
  display: flex;
  align-items: center;
}

.info-title::before {
  content: '📊';
  margin-right: 8px;
  font-size: 1rem;
}

.group-examples .info-title::before {
  content: '👥';
}

.player-list .info-title::before {
  content: '🎮';
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
}

.info-label {
  color: #ccc;
  font-size: 0.9rem;
}

.info-value {
  color: #ffa500;
  font-weight: bold;
  font-size: 0.9rem;
}

.group-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: rgba(40, 45, 55, 0.8);
  border-radius: 6px;
  border-left: 3px solid #ffa500;
}

.group-name {
  color: #ffa500;
  font-weight: bold;
  margin-right: 10px;
  min-width: 50px;
}

.group-desc {
  color: #e0e0e0;
  font-size: 0.9rem;
}

.players {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.player-item {
  display: flex;
  align-items: center;
  padding: 6px 10px;
  background: rgba(40, 45, 55, 0.8);
  border-radius: 6px;
  border-left: 3px solid #00ff00;
}

.player-number {
  color: #00ff00;
  font-weight: bold;
  margin-right: 10px;
  min-width: 80px;
}

.player-name {
  color: #e0e0e0;
  font-size: 0.9rem;
}

.player-note {
  color: #888;
  font-size: 0.8rem;
  font-style: italic;
  margin-top: 8px;
  text-align: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}





@media (max-width: 768px) {
  .page-title {
    font-size: 1.5rem;
  }

  .title-icon {
    font-size: 1.8rem;
  }

  .header-content {
    flex-direction: column;
    gap: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .events-grid {
    grid-template-columns: 1fr;
  }

  .tournament-info,
  .group-examples,
  .player-list {
    padding: 12px;
    margin-top: 15px;
  }

  .info-title {
    font-size: 1rem;
  }

  .group-item,
  .player-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .group-name,
  .player-number {
    min-width: auto;
    margin-right: 0;
  }
}
</style>
