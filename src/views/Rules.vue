<template>
  <div class="rules-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="title-icon">📋</i>
            规则讲解
          </h1>
          <p class="page-subtitle">PUBG抽卡系统游戏规则详解</p>
        </div>
      </div>
    </div>

    <!-- 规则内容 -->
    <div class="rules-content">
      <!-- 基础规则 -->
      <div class="rule-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="section-icon">🎯</i>
            基础规则
          </h2>
        </div>
        <div class="rule-cards">
          <div class="rule-card">
            <div class="card-header">
              <i class="card-icon">💰</i>
              <h3>抽卡消耗</h3>
            </div>
            <div class="card-content">
              <p>每次抽卡消耗 <span class="highlight">100金币</span></p>
              <p>十连抽消耗 <span class="highlight">900金币</span>（优惠10%）</p>
            </div>
          </div>

          <div class="rule-card">
            <div class="card-header">
              <i class="card-icon">🎁</i>
              <h3>奖励类型</h3>
            </div>
            <div class="card-content">
              <p>• 武器皮肤</p>
              <p>• 角色服装</p>
              <p>• 载具涂装</p>
              <p>• 特殊道具</p>
            </div>
          </div>

          <div class="rule-card">
            <div class="card-header">
              <i class="card-icon">⭐</i>
              <h3>稀有度等级</h3>
            </div>
            <div class="card-content">
              <p><span class="rarity common">普通</span> - 60%概率</p>
              <p><span class="rarity rare">稀有</span> - 25%概率</p>
              <p><span class="rarity epic">史诗</span> - 12%概率</p>
              <p><span class="rarity legendary">传说</span> - 3%概率</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 抽卡机制 -->
      <div class="rule-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="section-icon">🎲</i>
            抽卡机制
          </h2>
        </div>
        <div class="mechanism-list">
          <div class="mechanism-item">
            <div class="mechanism-number">1</div>
            <div class="mechanism-content">
              <h4>保底机制</h4>
              <p>连续9次未获得稀有以上物品，第10次必出稀有或以上</p>
            </div>
          </div>

          <div class="mechanism-item">
            <div class="mechanism-number">2</div>
            <div class="mechanism-content">
              <h4>大保底机制</h4>
              <p>连续49次未获得传说物品，第50次必出传说物品</p>
            </div>
          </div>

          <div class="mechanism-item">
            <div class="mechanism-number">3</div>
            <div class="mechanism-content">
              <h4>重复保护</h4>
              <p>获得重复物品时，将转换为对应数量的金币或材料</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 特殊活动 -->
      <div class="rule-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="section-icon">🎉</i>
            特殊活动
          </h2>
        </div>
        <div class="activity-grid">
          <div class="activity-card">
            <div class="activity-header">
              <h4>每日首抽</h4>
              <span class="activity-tag">每日</span>
            </div>
            <p>每日首次抽卡享受5折优惠</p>
          </div>

          <div class="activity-card">
            <div class="activity-header">
              <h4>周末狂欢</h4>
              <span class="activity-tag">周末</span>
            </div>
            <p>周末期间传说物品概率提升至5%</p>
          </div>

          <div class="activity-card">
            <div class="activity-header">
              <h4>节日庆典</h4>
              <span class="activity-tag">节日</span>
            </div>
            <p>特殊节日推出限定皮肤和双倍奖励</p>
          </div>
        </div>
      </div>

      <!-- 注意事项 -->
      <div class="rule-section">
        <div class="section-header">
          <h2 class="section-title">
            <i class="section-icon">⚠️</i>
            注意事项
          </h2>
        </div>
        <div class="notice-box">
          <ul class="notice-list">
            <li>抽卡结果完全随机，请理性消费</li>
            <li>所有概率均为理论概率，实际结果可能存在偏差</li>
            <li>活动期间的特殊规则以官方公告为准</li>
            <li>如遇到技术问题，请及时联系客服</li>
            <li>未成年人需在监护人同意下进行游戏</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Rules',
  mounted() {
    // 页面加载动画
    this.animateCards()
  },
  methods: {
    animateCards() {
      const cards = document.querySelectorAll('.rule-card, .mechanism-item, .activity-card')
      cards.forEach((card, index) => {
        setTimeout(() => {
          card.style.opacity = '1'
          card.style.transform = 'translateY(0)'
        }, index * 100)
      })
    }
  }
}
</script>

<style scoped>
/* 页面容器 */
.rules-container {
  min-height: 100vh;
  background: linear-gradient(135deg, 
    rgba(20, 25, 35, 0.95) 0%, 
    rgba(35, 40, 50, 0.95) 50%, 
    rgba(20, 25, 35, 0.95) 100%),
    url('/bg_fixed2_1080p_001.ts');
  background-size: cover;
  background-attachment: fixed;
  padding-top: 80px;
}

/* 页面标题 */
.page-header {
  padding: 40px 0;
  text-align: center;
  border-bottom: 1px solid rgba(255, 165, 0, 0.3);
  margin-bottom: 40px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-title {
  font-size: 2rem;
  color: #ffa500;
  margin-bottom: 8px;
  text-shadow:
    0 0 8px rgba(255, 165, 0, 0.8),
    0 0 16px rgba(255, 165, 0, 0.4);
  font-weight: 800;
}

.title-icon {
  margin-right: 10px;
  font-size: 2.2rem;
  filter: drop-shadow(0 0 8px rgba(255, 165, 0, 0.8));
}

.page-subtitle {
  font-size: 1rem;
  color: #cccccc;
  margin: 0;
  letter-spacing: 1px;
}

/* 规则内容 */
.rules-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px 60px;
}

/* 规则章节 */
.rule-section {
  margin-bottom: 60px;
}

.section-header {
  margin-bottom: 30px;
}

.section-title {
  font-size: 1.5rem;
  color: #ffa500;
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  text-shadow: 0 0 4px rgba(255, 165, 0, 0.6);
}

.section-icon {
  margin-right: 8px;
  font-size: 1.6rem;
  filter: drop-shadow(0 0 4px rgba(255, 165, 0, 0.8));
}

/* 规则卡片 */
.rule-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
}

.rule-card {
  background: linear-gradient(135deg,
    rgba(40, 45, 55, 0.9),
    rgba(30, 35, 45, 0.9));
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 10px;
  padding: 18px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.5);
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
}

.rule-card:hover {
  transform: translateY(-5px);
  border-color: #ffa500;
  box-shadow: 0 12px 35px rgba(255, 165, 0, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.card-icon {
  font-size: 1.4rem;
  margin-right: 8px;
  filter: drop-shadow(0 0 4px rgba(255, 165, 0, 0.6));
}

.card-header h3 {
  color: #ffa500;
  font-size: 1.1rem;
  margin: 0;
  font-weight: 600;
}

.card-content {
  color: #e0e0e0;
  line-height: 1.6;
}

.card-content p {
  margin: 8px 0;
}

.highlight {
  color: #ffa500;
  font-weight: bold;
  text-shadow: 0 0 3px rgba(255, 165, 0, 0.6);
}

/* 稀有度标签 */
.rarity {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 0.9rem;
}

.rarity.common { background: #808080; color: #fff; }
.rarity.rare { background: #4169e1; color: #fff; }
.rarity.epic { background: #9932cc; color: #fff; }
.rarity.legendary { background: #ffa500; color: #000; }

/* 抽卡机制列表 */
.mechanism-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mechanism-item {
  display: flex;
  align-items: flex-start;
  background: linear-gradient(135deg,
    rgba(40, 45, 55, 0.9),
    rgba(30, 35, 45, 0.9));
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 12px;
  padding: 25px;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.3s ease;
}

.mechanism-item:hover {
  border-color: #ffa500;
  box-shadow: 0 8px 25px rgba(255, 165, 0, 0.2);
}

.mechanism-number {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
  color: #000;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.2rem;
  margin-right: 20px;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(255, 165, 0, 0.4);
}

.mechanism-content h4 {
  color: #ffa500;
  margin: 0 0 8px 0;
  font-size: 1.2rem;
}

.mechanism-content p {
  color: #e0e0e0;
  margin: 0;
  line-height: 1.6;
}

/* 活动网格 */
.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.activity-card {
  background: linear-gradient(135deg,
    rgba(40, 45, 55, 0.9),
    rgba(30, 35, 45, 0.9));
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
}

.activity-card:hover {
  transform: translateY(-3px);
  border-color: #ffa500;
  box-shadow: 0 8px 25px rgba(255, 165, 0, 0.2);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.activity-header h4 {
  color: #ffa500;
  margin: 0;
  font-size: 1.1rem;
}

.activity-tag {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
  color: #000;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
}

.activity-card p {
  color: #e0e0e0;
  margin: 0;
  line-height: 1.5;
}

/* 注意事项 */
.notice-box {
  background: linear-gradient(135deg,
    rgba(60, 30, 30, 0.9),
    rgba(50, 25, 25, 0.9));
  border: 1px solid rgba(255, 100, 100, 0.4);
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

.notice-list {
  color: #ffcccc;
  line-height: 1.8;
  margin: 0;
  padding-left: 20px;
}

.notice-list li {
  margin-bottom: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 1.5rem;
  }

  .title-icon {
    font-size: 1.8rem;
  }

  .section-title {
    font-size: 1.2rem;
  }

  .section-icon {
    font-size: 1.3rem;
  }

  .rule-cards,
  .activity-grid {
    grid-template-columns: 1fr;
  }

  .rules-content {
    padding: 0 15px 40px;
  }

  .mechanism-item {
    flex-direction: column;
    text-align: center;
  }

  .mechanism-number {
    margin: 0 0 15px 0;
  }
}
</style>
