<template>
  <div class="login-container">
    <!-- 游戏风格登录卡片 -->
    <div class="login-card">
      <!-- 绝地求生风格标题 -->
      <div class="game-title">
        <img src="/public/Unknown-5.png" alt="PUBG Logo" class="game-logo">
        <h1>战场登录</h1>
        <div class="military-line"></div>
      </div>

      <!-- 登录表单 -->
      <form class="login-form" @submit.prevent="handleLogin">
        <!-- 军事风格输入框 -->
        <div class="form-group military-input">
          <label class="input-label">士兵代号</label>
          <input 
            type="text" 
            v-model="username" 
            required 
            placeholder="输入用户名"
            class="military-input-field"
          >
          <div class="input-glow"></div>
        </div>

        <div class="form-group military-input">
          <label class="input-label">作战密码</label>
          <input 
            type="password" 
            v-model="password" 
            required 
            placeholder="输入密码"
            class="military-input-field"
          >
          <div class="input-glow"></div>
        </div>

        <!-- 游戏风格按钮 -->
        <button type="submit" class="military-btn" :disabled="loading">
          <span class="btn-text">{{ loading ? '登录中...' : '进入战场' }}</span>
          <div class="btn-glow"></div>
        </button>
      </form>

      <!-- 错误提示 -->
      <div class="error-alert" v-if="error">
        <i class="alert-icon">!</i> {{ error }}
      </div>

      <!-- 注册链接 -->
      <!-- <div class="register-link">
        没有账号? <router-link to="/register" class="military-link">新兵注册</router-link>
      </div> -->
    </div>
  </div>
</template>

<script>
import { login } from '../api.js'

/**
 * 绝地求生风格登录组件
 * 实现游戏化UI设计，包含军事风格元素和动画效果
 */
export default {
  name: 'Login',
  data() {
    return {
      username: '',
      password: '',
      loading: false,
      error: ''
    }
  },
  methods: {
    /**
     * 处理登录逻辑
     * @async
     * @returns {Promise<void>}
     */
    async handleLogin() {
      this.error = ''
      this.loading = true
      try {
        const res = await login(this.username, this.password)
        localStorage.setItem('user', JSON.stringify(res.user))
        this.$router.push('/dashboard')
      } catch (e) {
        this.error = e.msg || '登录失败，请检查账号密码'
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style scoped>
/* 基础样式重置与全局设置 */
.login-container {
  position: relative;
  min-height: 100vh;
  overflow: hidden;
  /* 添加Flexbox布局实现居中 */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 背景视频样式 */
.bg-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1;
  filter: brightness(0.4);
}

/* 登录卡片样式 - 军事装备箱风格 */
.login-card {
  position: relative;
  z-index: 2;
  max-width: 420px;
  width: 90%; /* 添加响应式宽度 */
  padding: 30px;
  background: rgba(18, 22, 30, 0.95);
  border: 2px solid rgba(101, 67, 33, 0.6);
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.8),
              inset 0 0 10px rgba(165, 42, 42, 0.3);
  /* 移除垂直位移 */
  transform: none;
}

/* 游戏标题样式 */
.game-title {
  text-align: center;
  margin-bottom: 30px;
}

.game-logo {
  height: 60px;
  margin-bottom: 10px;
  filter: drop-shadow(0 0 8px rgba(245, 194, 66, 0.6));
}

.game-title h1 {
  font-family: 'Arial Black', sans-serif;
  font-size: 2.2rem;
  color: #f5c242;
  text-shadow: 0 0 8px rgba(245, 194, 66, 0.6),
               0 2px 4px rgba(0, 0, 0, 0.8);
  margin: 10px 0;
}

.military-line {
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5c242, transparent);
  margin: 15px 0;
}

/* 表单样式 */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.form-group {
  position: relative;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  color: #b8860b;
  font-size: 0.9rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 军事风格输入框 */
.military-input-field {
  width: 100%;
  padding: 14px;
  box-sizing: border-box;
  background: rgba(18, 22, 30, 0.8);
  border: 1px solid #5a4016;
  border-radius: 4px;
  color: #fff;
  font-size: 1rem;
  box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.6);
  transition: all 0.3s;
}

.military-input-field:focus {
  outline: none;
  border-color: #f5c242;
  box-shadow: 0 0 10px rgba(245, 194, 66, 0.5);
}

.input-glow {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5c242, transparent);
  opacity: 0;
  transition: opacity 0.3s;
}

.military-input-field:focus + .input-glow {
  opacity: 1;
}

/* 军事风格按钮 */
.military-btn {
  position: relative;
  background: linear-gradient(180deg, #5a4016, #b8860b);
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 14px 0;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  overflow: hidden;
  transition: all 0.3s;
}

.military-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(245, 194, 66, 0.4);
}

.btn-text {
  position: relative;
  z-index: 2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: all 0.6s;
}

.military-btn:hover .btn-glow {
  left: 100%;
}

/* 错误提示 */
.error-alert {
  background: rgba(255, 77, 79, 0.2);
  border: 1px solid #ff4d4f;
  border-radius: 4px;
  padding: 10px;
  color: #ff4d4f;
  display: flex;
  align-items: center;
  gap: 8px;
}

.alert-icon {
  background: #ff4d4f;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

/* 注册链接 */
.register-link {
  margin-top: 25px;
  text-align: center;
  color: #b8860b;
}

.military-link {
  color: #f5c242;
  text-decoration: none;
  font-weight: bold;
  transition: all 0.2s;
}

.military-link:hover {
  text-shadow: 0 0 8px rgba(245, 194, 66, 0.6);
}
</style>