<template>
  <div class="box-opening-container">
    <header class="box-header">
      <router-link to="/dashboard" class="back-btn">返回</router-link>
      <h2>开箱页面</h2>
    </header>
    <section class="box-list">
      <div class="box-card" v-for="box in boxes" :key="box.type">
        <h3>{{ box.name }}</h3>
        <p>{{ box.desc }}</p>
        <div class="box-info">
          <span>价格：{{ box.price }} 钥匙</span>
          <span>稀有度概率：{{ box.prob }}</span>
        </div>
        <button class="btn" @click="openBox(box.type, 1)">单抽</button>
        <button class="btn" @click="openBox(box.type, 10)">十连抽</button>
      </div>
    </section>
    <section class="animation-area">
      <p>这里是卡片翻转/揭晓动画区（待实现）</p>
    </section>
    <section class="result-modal" v-if="showResult">
      <h3>获得卡片</h3>
      <ul>
        <li v-for="card in resultCards" :key="card.id">
          <span :style="{color: card.rarityColor}">{{ card.name }}</span>
        </li>
      </ul>
      <button class="btn" @click="showResult=false">继续开箱</button>
    </section>
    <section class="stats-area">
      <h4>统计区</h4>
      <p>总开箱次数：{{ stats.total }}</p>
      <p>各稀有度获得数：{{ stats.rarity }}</p>
    </section>
  </div>
</template>

<script>
export default {
  name: 'BoxOpening',
  data() {
    return {
      boxes: [
        { type: 'basic', name: '基础宝箱', desc: '普通奖励，适合新手', price: 1, prob: '常规', },
        { type: 'advanced', name: '高级宝箱', desc: '更高概率获得稀有物品', price: 3, prob: '稀有', },
        { type: 'legendary', name: '传说宝箱', desc: '最高概率获得传说物品', price: 8, prob: '传说', },
      ],
      showResult: false,
      resultCards: [],
      stats: {
        total: 0,
        rarity: '普通:0 稀有:0 传说:0',
      },
    }
  },
  methods: {
    openBox(type, count) {
      // 这里后续对接抽卡逻辑
      this.resultCards = [
        { id: 1, name: 'AKM', rarityColor: '#fff' },
        { id: 2, name: '三级头', rarityColor: '#f5c242' },
      ]
      this.showResult = true
      this.stats.total += count
    },
  },
}
</script>

<style scoped>
.box-opening-container {
  max-width: 1100px;
  margin: 40px auto;
  background: #23262e;
  border-radius: 16px;
  box-shadow: 0 4px 24px #0006;
  padding-bottom: 32px;
}
.box-header {
  display: flex;
  align-items: center;
  gap: 32px;
  padding: 24px 32px 0 32px;
}
.back-btn {
  color: #f5c242;
  text-decoration: none;
  font-size: 18px;
}
.box-list {
  display: flex;
  justify-content: center;
  gap: 32px;
  margin: 32px 0;
}
.box-card {
  background: #181a20;
  border-radius: 12px;
  padding: 24px 32px;
  min-width: 220px;
  box-shadow: 0 2px 8px #0004;
  text-align: center;
}
.box-info {
  margin: 12px 0 18px 0;
  color: #f5c242;
}
.btn {
  display: inline-block;
  margin: 8px 8px 0 8px;
  padding: 10px 24px;
  background: linear-gradient(90deg, #f5c242, #b8860b);
  color: #181a20;
  font-weight: bold;
  border-radius: 8px;
  text-decoration: none;
  font-size: 16px;
  border: none;
  cursor: pointer;
  transition: box-shadow 0.2s;
}
.btn:hover {
  box-shadow: 0 4px 16px #f5c24288;
}
.animation-area {
  min-height: 120px;
  background: #181a20;
  margin: 32px 32px 0 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #888;
}
.result-modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #23262e;
  border-radius: 12px;
  box-shadow: 0 4px 24px #000a;
  padding: 32px 48px;
  z-index: 1000;
  text-align: center;
}
.result-modal ul {
  list-style: none;
  padding: 0;
  margin: 18px 0;
}
.result-modal li {
  font-size: 20px;
  margin: 8px 0;
}
.stats-area {
  margin: 32px 32px 0 32px;
  background: #181a20;
  border-radius: 8px;
  padding: 18px 24px;
  color: #f5c242;
}
</style> 