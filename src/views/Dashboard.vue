<template>
  <div class="dashboard">
    <!-- 背景图片 -->
    <div class="background-image"></div>
    
    <!-- 左下角抽卡区域 -->
    <div class="draw-section">
      <div class="draw-info">
        <div class="draw-title">魔法迷雾</div>
        <div class="draw-subtitle">高级宝箱·限时开启</div>
        <div class="draw-description">神秘的魔法迷雾中蕴含着强大的力量</div>
      </div>
      <button class="draw-btn" @click="drawTen()">
        <div class="btn-content">
          <i class="btn-icon">💎</i>
          <span class="btn-text">开启10次</span>
          <span class="btn-cost">消耗 1,080</span>
        </div>
      </button>
    </div>

    <!-- 右下角人物/怪物 -->
    <div class="character-section">
      <div class="character-image">
        <!-- <img src="/character.png" alt="角色" class="character" /> -->
        <!-- <img src="/jb.png" alt="角色" class="character" /> -->
      </div>
    </div>

    <!-- 抽卡弹窗 - 视频播放和结果展示 -->
    <div v-if="showDrawModal || showResultModal" class="draw-fullscreen-overlay">
      <video
        ref="drawVideo"
        :src="currentDrawVideo"
        class="draw-video-fullscreen"
        autoplay
        muted
        @ended="onVideoEnded"
        @click="skipVideo"
      ></video>

      <!-- 跳过按钮 - 只在播放阶段显示 -->
      <button v-if="showDrawModal" class="skip-btn" @click="skipVideo">跳过</button>

      <!-- 抽卡结果展示 - 在视频上方 -->
      <div v-if="showResultModal" class="result-overlay">
        <div class="result-modal">
          <div class="result-header">
            <h3>打开黑货箱</h3>
          </div>
          <div class="result-content">
            <div class="result-grid">
              <div
                v-for="(card, index) in currentDrawResult"
                :key="index"
                class="result-card"
                :class="{ 'flipped': card.isFlipped }"
                @click="flipCard(index)"
              >
                <div class="card-inner">
                  <!-- 卡牌背面 -->
                  <div class="card-back"
                       :style="{ backgroundImage: `url(${card.rarity === '黄金' ? '/jin.png' : '/yin.png'})` }">
                  </div>
                  <!-- 卡牌正面 -->
                  <div class="card-front" :class="card.rarity">
                    <div class="card-icon">{{ card.icon }}</div>
                    <div class="card-name">{{ card.name }}</div>
                    <div class="card-rarity">{{ card.rarity }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="result-footer">
            <!-- <button class="flip-all-btn" @click="flipAllCards" v-if="!allCardsFlipped">全部翻开</button> -->
            <button class="close-btn" @click="closeResult">关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      singleBox: {
        id: 1,
        name: '魔法迷雾',
        desc: '神秘的魔法迷雾中蕴含着强大的力量',
        // img: '/box.png',
        price: 108
      },
      showDrawModal: false,
      showResultModal: false,
      currentDrawVideo: '',
      currentDrawResult: [],
      allCardsFlipped: false,
      drawHistory: [],
      statistics: {
        totalDraws: 0,
        goldCount: 0,
        silverCount: 0
      }
    }
  },
  methods: {
    async drawTen() {
      try {
        this.showDrawModal = true;
        
        // 模拟API调用
        const response = await this.simulateDrawAPI(10);
        this.currentDrawResult = response.cards;
        this.currentDrawVideo = response.videoUrl;
        
        // 更新统计
        this.updateStatistics(response.cards);
        
      } catch (error) {
        console.error('抽卡失败:', error);
        this.showDrawModal = false;
      }
    },

    async simulateDrawAPI(count) {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const cards = [];
      const hasGold = Math.random() > 0.7; // 30% 概率出金卡
      const goldCount = hasGold ? (Math.random() > 0.8 ? 2 : 1) : 0;
      
      // 生成卡牌
      for (let i = 0; i < count; i++) {
        if (i < goldCount) {
          cards.push({
            name: `黄金装备${i + 1}`,
            rarity: '黄金',
            icon: '🏆',
            isFlipped: false
          });
        } else {
          cards.push({
            name: `白银装备${i + 1}`,
            rarity: '白银',
            icon: '🥈',
            isFlipped: false
          });
        }
      }
      
      // 选择视频
      const videoUrl = goldCount > 0 ? '/gold_box.mp4' : '/silver_box.mp4';
      
      return { cards, videoUrl };
    },

    onVideoEnded() {
      // 视频播放完后，切换到结果展示状态，但保持视频作为背景
      this.showDrawModal = false;
      this.showResultModal = true;
      this.allCardsFlipped = false;
    },

    skipVideo() {
      // 跳过视频到最后一帧
      const video = this.$refs.drawVideo;
      if (video) {
        if (video.duration) {
          video.currentTime = video.duration - 0.1; // 跳到最后一帧前0.1秒
        } else {
          // 如果duration还没加载，等待loadedmetadata事件
          video.addEventListener('loadedmetadata', () => {
            video.currentTime = video.duration - 0.1;
          }, { once: true });
        }
      }
    },

    flipCard(index) {
      if (this.currentDrawResult[index]) {
        this.currentDrawResult[index].isFlipped = true;
        this.checkAllCardsFlipped();
      }
    },

    flipAllCards() {
      this.currentDrawResult.forEach(card => {
        card.isFlipped = true;
      });
      this.allCardsFlipped = true;
    },

    checkAllCardsFlipped() {
      this.allCardsFlipped = this.currentDrawResult.every(card => card.isFlipped);
    },

    closeResult() {
      this.showDrawModal = false;
      this.showResultModal = false;
      this.currentDrawResult = [];
      this.allCardsFlipped = false;
    },

    updateStatistics(cards) {
      this.statistics.totalDraws += cards.length;
      cards.forEach(card => {
        if (card.rarity === '黄金') {
          this.statistics.goldCount++;
        } else {
          this.statistics.silverCount++;
        }
      });
      
      // 添加到历史记录
      const now = new Date();
      const timeStr = `${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}`;
      
      cards.forEach(card => {
        this.drawHistory.unshift({
          time: timeStr,
          pack: this.singleBox.name,
          cardName: card.name,
          rarity: card.rarity
        });
      });
      
      // 限制历史记录数量
      if (this.drawHistory.length > 100) {
        this.drawHistory = this.drawHistory.slice(0, 100);
      }
    }
  }
}
</script>

<style scoped>
.dashboard {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 200px;
  padding-bottom: 0;
  box-sizing: border-box;
  height: 100%;
  background: url('/dashboard-bg.png') center/cover no-repeat;
}

.draw-section {
  position: absolute;
  bottom: 80px;
  left: 60px;
  z-index: 10;
}

.draw-info {
  margin-bottom: 20px;
}

.draw-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffa500;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  margin-bottom: 8px;
}

.draw-subtitle {
  font-size: 1.2rem;
  color: #e0e0e0;
  margin-bottom: 8px;
}

.draw-description {
  font-size: 1rem;
  color: #ccc;
  max-width: 300px;
}

.draw-btn {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
  border: none;
  border-radius: 12px;
  padding: 15px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.4);
}

.draw-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 165, 0, 0.6);
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn-icon {
  font-size: 1.5rem;
}

.btn-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
}

.btn-cost {
  font-size: 0.9rem;
  color: #fff;
  opacity: 0.9;
}

.character-section {
  position: absolute;
  bottom: 0;
  right: 60px;
  z-index: 10;
}

.character {
  max-height: 400px;
  max-width: 300px;
  object-fit: contain;
}

/* 抽卡动画弹窗 */
.draw-fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.draw-video-fullscreen {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
}

.skip-btn {
  position: absolute;
  top: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid #ffa500;
  color: #ffa500;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.skip-btn:hover {
  background: rgba(255, 165, 0, 0.2);
}

/* 结果展示覆盖层 */
.result-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.result-modal {
  background: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 20px;
  width: 95vw;
  height: 85vh;
  border: 2px solid #ffa500;
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-header h3 {
  color: #fff;
  text-align: center;
  margin-bottom: 10px;
  font-size: 1.8rem;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.result-card {
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  aspect-ratio: 1/1;
  min-width: 120px;
  min-height: 120px;
  perspective: 1000px;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.result-card.flipped .card-inner {
  transform: rotateY(180deg);
}

.result-card:hover {
  transform: scale(1.02);
}

.result-card:hover .card-inner {
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}



.card-back, .card-front {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.card-back {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.card-front {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  transform: rotateY(180deg);
  padding: 10px;
  box-sizing: border-box;
}

/* 稀有度样式 */
.card-front.黄金 {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.card-front.白银 {
  border-color: #c0c0c0;
  background: rgba(192, 192, 192, 0.1);
  box-shadow: 0 0 10px rgba(192, 192, 192, 0.3);
}



.card-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.card-name {
  color: white;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-rarity {
  font-size: 0.9rem;
  opacity: 0.8;
}

.card-front.黄金 .card-rarity {
  color: #ffd700;
}

.card-front.白银 .card-rarity {
  color: #c0c0c0;
}

.result-footer {
  text-align: center;
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 15px;
  padding-top: 10px;
}

.flip-all-btn {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
  border: none;
  color: white;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.flip-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.4);
}

.close-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border: none;
  color: white;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

@media (max-width: 768px) {
  .draw-section {
    bottom: 40px;
    left: 20px;
  }

  .character-section {
    right: 20px;
  }

  .draw-title {
    font-size: 2rem;
  }

  .character {
    max-height: 250px;
    max-width: 200px;
  }

  .result-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    max-width: 95vw;
    padding: 10px;
  }

  .result-card {
    border-radius: 6px;
    min-width: 60px;
    min-height: 60px;
  }

  .result-modal {
    padding: 20px;
    width: 95vw;
    height: 85vh;
  }
}
</style>
