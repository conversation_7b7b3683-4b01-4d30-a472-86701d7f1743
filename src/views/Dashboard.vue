<template>
  <div class="dashboard">
    <!-- 背景图片 -->
    <div class="background-image"></div>
    
    <!-- 左下角抽卡区域 -->
    <div class="draw-section">
      <div class="draw-info">
        <div class="draw-title">魔法迷雾</div>
        <div class="draw-subtitle">高级宝箱·限时开启</div>
        <div class="draw-description">神秘的魔法迷雾中蕴含着强大的力量</div>
      </div>
      <button class="draw-btn" @click="drawTen()">
        <div class="btn-content">
          <i class="btn-icon">💎</i>
          <span class="btn-text">开启10次</span>
          <span class="btn-cost">消耗 1,080</span>
        </div>
      </button>
    </div>

    <!-- 右下角人物/怪物 -->
    <div class="character-section">
      <div class="character-image">
        <!-- <img src="/character.png" alt="角色" class="character" /> -->
        <!-- <img src="/jb.png" alt="角色" class="character" /> -->
      </div>
    </div>

    <!-- 抽卡弹窗 - 视频播放和结果展示 -->
    <div v-if="showDrawModal || showResultModal" class="draw-fullscreen-overlay">
      <video
        ref="drawVideo"
        :src="currentDrawVideo"
        class="draw-video-fullscreen"
        autoplay
        muted
        @ended="onVideoEnded"
        @click="skipVideo"
      ></video>

      <!-- 跳过按钮 - 只在播放阶段显示 -->
      <button v-if="showDrawModal" class="skip-btn" @click="skipVideo">跳过</button>

      <!-- 抽卡结果展示 - 在视频上方 -->
      <div v-if="showResultModal" class="result-overlay">
        <div class="result-modal">
          <div class="result-header">
            <h3>打开黑货箱</h3>
          </div>
          <div class="result-content">
            <div class="result-grid">
              <div
                v-for="(card, index) in currentDrawResult"
                :key="index"
                class="result-card"
                :class="{ 'flipped': card.isFlipped }"
                @click="flipCard(index)"
              >
                <div class="card-inner">
                  <!-- 卡牌背面 -->
                  <div class="card-back"
                       :style="{ backgroundImage: `url(${card.rarity === '黄金' ? '/jin.png' : '/yin.png'})` }">
                  </div>
                  <!-- 卡牌正面 -->
                  <div class="card-front" :class="card.rarity">
                    <div class="card-icon">{{ card.icon }}</div>
                    <div class="card-name">{{ card.name }}</div>
                    <div class="card-rarity">{{ card.rarity }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="result-footer">
            <!-- <button class="flip-all-btn" @click="flipAllCards" v-if="!allCardsFlipped">全部翻开</button> -->
            <button class="close-btn" @click="closeResult">关闭</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 卡片详情弹窗 -->
    <div v-if="showCardDetailModal" class="modal-overlay" @click="closeCardDetail">
      <div class="card-detail-modal" @click.stop>
        <div class="detail-header">
          <h3>🎉 恭喜获得稀有装备！</h3>
        </div>

        <div class="detail-content">
          <div class="weapon-image">
            <img :src="selectedCard?.image" :alt="selectedCard?.name" class="weapon-img">
          </div>

          <div class="weapon-info">
            <div class="rarity-badge" :class="selectedCard?.rarity">
              {{ selectedCard?.rarity }}装备
            </div>
            <div class="weapon-name">
              {{ selectedCard?.name }}
            </div>
            <div class="weapon-description">
              这是一件珍贵的{{ selectedCard?.rarity }}级装备，将为你在战场上提供强大的火力支援！
            </div>
          </div>
        </div>

        <div class="detail-footer">
          <button class="close-detail-btn" @click="closeCardDetail">
            <i class="icon">✓</i> 确认收下
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      singleBox: {
        id: 1,
        name: '魔法迷雾',
        desc: '神秘的魔法迷雾中蕴含着强大的力量',
        // img: '/box.png',
        price: 108
      },
      showDrawModal: false,
      showResultModal: false,
      showCardDetailModal: false,
      selectedCard: null,
      currentDrawVideo: '',
      currentDrawResult: [],
      allCardsFlipped: false,
      drawHistory: [],
      statistics: {
        totalDraws: 0,
        goldCount: 0,
        silverCount: 0
      }
    }
  },
  methods: {
    async drawTen() {
      try {
        this.showDrawModal = true;
        
        // 模拟API调用
        const response = await this.simulateDrawAPI(10);
        this.currentDrawResult = response.cards;
        this.currentDrawVideo = response.videoUrl;
        
        // 更新统计
        this.updateStatistics(response.cards);
        
      } catch (error) {
        console.error('抽卡失败:', error);
        this.showDrawModal = false;
      }
    },

    async simulateDrawAPI(count) {
      // 模拟API延迟
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const cards = [];
      const hasGold = Math.random() > 0.7; // 30% 概率出金卡
      const goldCount = hasGold ? (Math.random() > 0.8 ? 2 : 1) : 0;
      
      // 武器数据库
      const goldWeapons = [
        { name: 'AKM突击步枪', icon: '🏆', image: '/weapons/akm.png' },
        { name: 'M416突击步枪', icon: '🏆', image: '/weapons/m416.png' },
        { name: 'AWM狙击步枪', icon: '🏆', image: '/weapons/awm.png' },
        { name: 'M249机枪', icon: '🏆', image: '/weapons/m249.png' }
      ];

      const silverWeapons = [
        { name: 'M16A4步枪', icon: '🥈', image: '/weapons/m16a4.png' },
        { name: 'SCAR-L步枪', icon: '🥈', image: '/weapons/scar.png' },
        { name: 'UMP45冲锋枪', icon: '🥈', image: '/weapons/ump45.png' },
        { name: 'S686霰弹枪', icon: '🥈', image: '/weapons/s686.png' },
        { name: 'Kar98k狙击枪', icon: '🥈', image: '/weapons/kar98k.png' }
      ];

      // 生成卡牌
      for (let i = 0; i < count; i++) {
        if (i < goldCount) {
          const weapon = goldWeapons[Math.floor(Math.random() * goldWeapons.length)];
          cards.push({
            name: weapon.name,
            rarity: '黄金',
            icon: weapon.icon,
            image: weapon.image,
            isFlipped: false
          });
        } else {
          const weapon = silverWeapons[Math.floor(Math.random() * silverWeapons.length)];
          cards.push({
            name: weapon.name,
            rarity: '白银',
            icon: weapon.icon,
            image: weapon.image,
            isFlipped: false
          });
        }
      }
      
      // 选择视频
      const videoUrl = goldCount > 0 ? '/gold_box.mp4' : '/silver_box.mp4';
      
      return { cards, videoUrl };
    },

    onVideoEnded() {
      // 视频播放完后，切换到结果展示状态，但保持视频作为背景
      this.showDrawModal = false;
      this.showResultModal = true;
      this.allCardsFlipped = false;
    },

    skipVideo() {
      // 跳过视频到最后一帧
      const video = this.$refs.drawVideo;
      if (video) {
        if (video.duration) {
          video.currentTime = video.duration - 0.1; // 跳到最后一帧前0.1秒
        } else {
          // 如果duration还没加载，等待loadedmetadata事件
          video.addEventListener('loadedmetadata', () => {
            video.currentTime = video.duration - 0.1;
          }, { once: true });
        }
      }
    },

    flipCard(index) {
      if (this.currentDrawResult[index] && !this.currentDrawResult[index].isFlipped) {
        this.currentDrawResult[index].isFlipped = true;

        // 立即显示翻开的卡片详情
        this.selectedCard = this.currentDrawResult[index];

        // 延迟显示详情弹窗，让翻卡动画完成
        setTimeout(() => {
          this.showCardDetailModal = true;
        }, 600);

        this.checkAllCardsFlipped();
      }
    },

    flipAllCards() {
      this.currentDrawResult.forEach(card => {
        card.isFlipped = true;
      });
      this.allCardsFlipped = true;
    },

    checkAllCardsFlipped() {
      this.allCardsFlipped = this.currentDrawResult.every(card => card.isFlipped);

      // 如果所有卡片都翻完了，显示详情弹窗
      if (this.allCardsFlipped) {
        // 选择一张最高稀有度的卡片作为展示
        const goldCards = this.currentDrawResult.filter(card => card.rarity === '黄金');
        const silverCards = this.currentDrawResult.filter(card => card.rarity === '白银');

        this.selectedCard = goldCards.length > 0 ? goldCards[0] : silverCards[0];

        // 延迟显示详情弹窗，让翻卡动画完成
        setTimeout(() => {
          this.showCardDetailModal = true;
        }, 800);
      }
    },

    closeResult() {
      this.showDrawModal = false;
      this.showResultModal = false;
      this.showCardDetailModal = false;
      this.currentDrawResult = [];
      this.allCardsFlipped = false;
      this.selectedCard = null;
    },

    closeCardDetail() {
      this.showCardDetailModal = false;
      this.selectedCard = null;
    },

    updateStatistics(cards) {
      this.statistics.totalDraws += cards.length;
      cards.forEach(card => {
        if (card.rarity === '黄金') {
          this.statistics.goldCount++;
        } else {
          this.statistics.silverCount++;
        }
      });
      
      // 添加到历史记录
      const now = new Date();
      const timeStr = `${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}`;
      
      cards.forEach(card => {
        this.drawHistory.unshift({
          time: timeStr,
          pack: this.singleBox.name,
          cardName: card.name,
          rarity: card.rarity
        });
      });
      
      // 限制历史记录数量
      if (this.drawHistory.length > 100) {
        this.drawHistory = this.drawHistory.slice(0, 100);
      }
    }
  }
}
</script>

<style scoped>
.dashboard {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  padding: 200px;
  padding-bottom: 0;
  box-sizing: border-box;
  height: 100%;
  background: url('/dashboard-bg.png') center/cover no-repeat;
}

.draw-section {
  position: absolute;
  bottom: 80px;
  left: 60px;
  z-index: 10;
}

.draw-info {
  margin-bottom: 20px;
}

.draw-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #ffa500;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  margin-bottom: 8px;
}

.draw-subtitle {
  font-size: 1.2rem;
  color: #e0e0e0;
  margin-bottom: 8px;
}

.draw-description {
  font-size: 1rem;
  color: #ccc;
  max-width: 300px;
}

.draw-btn {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
  border: none;
  border-radius: 12px;
  padding: 15px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.4);
}

.draw-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 165, 0, 0.6);
}

.btn-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn-icon {
  font-size: 1.5rem;
}

.btn-text {
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
}

.btn-cost {
  font-size: 0.9rem;
  color: #fff;
  opacity: 0.9;
}

.character-section {
  position: absolute;
  bottom: 0;
  right: 60px;
  z-index: 10;
}

.character {
  max-height: 400px;
  max-width: 300px;
  object-fit: contain;
}

/* 抽卡动画弹窗 */
.draw-fullscreen-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.draw-video-fullscreen {
  max-width: 90vw;
  max-height: 90vh;
  object-fit: contain;
}

.skip-btn {
  position: absolute;
  top: 30px;
  right: 30px;
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid #ffa500;
  color: #ffa500;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.skip-btn:hover {
  background: rgba(255, 165, 0, 0.2);
}

/* 结果展示覆盖层 */
.result-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.result-modal {
  background: rgba(0, 0, 0, 0.7);
  border-radius: 20px;
  padding: 20px;
  width: 95vw;
  height: 85vh;
  border: 2px solid #ffa500;
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-header h3 {
  color: #fff;
  text-align: center;
  margin-bottom: 10px;
  font-size: 1.8rem;
}

.result-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

.result-card {
  border-radius: 8px;
  cursor: pointer;
  position: relative;
  aspect-ratio: 1/1;
  min-width: 120px;
  min-height: 120px;
  perspective: 1000px;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s;
  transform-style: preserve-3d;
}

.result-card.flipped .card-inner {
  transform: rotateY(180deg);
}

.result-card:hover {
  transform: scale(1.02);
}

.result-card:hover .card-inner {
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}



.card-back, .card-front {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.card-back {
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.card-front {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid transparent;
  transform: rotateY(180deg);
  padding: 10px;
  box-sizing: border-box;
}

/* 稀有度样式 */
.card-front.黄金 {
  border-color: #ffd700;
  background: rgba(255, 215, 0, 0.1);
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.card-front.白银 {
  border-color: #c0c0c0;
  background: rgba(192, 192, 192, 0.1);
  box-shadow: 0 0 10px rgba(192, 192, 192, 0.3);
}



.card-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.card-name {
  color: white;
  font-weight: bold;
  margin-bottom: 5px;
}

.card-rarity {
  font-size: 0.9rem;
  opacity: 0.8;
}

.card-front.黄金 .card-rarity {
  color: #ffd700;
}

.card-front.白银 .card-rarity {
  color: #c0c0c0;
}

/* 卡片详情弹窗样式 */
.card-detail-modal {
  background: rgba(0, 0, 0, 0.9);
  border-radius: 20px;
  padding: 30px;
  width: 600px;
  max-width: 90vw;
  border: 2px solid #ffa500;
  backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  gap: 20px;
  animation: modalSlideIn 0.5s ease-out;
}

.detail-header h3 {
  color: #ffa500;
  text-align: center;
  margin: 0;
  font-size: 1.8rem;
  text-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

.detail-content {
  display: flex;
  gap: 30px;
  align-items: center;
}

.weapon-image {
  flex: 0 0 200px;
  text-align: center;
}

.weapon-img {
  width: 180px;
  height: 180px;
  object-fit: contain;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.1);
  padding: 10px;
  border: 2px solid rgba(255, 165, 0, 0.3);
}

.weapon-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.rarity-badge {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 1rem;
  text-align: center;
  border: 2px solid;
}

.rarity-badge.黄金 {
  background: rgba(255, 215, 0, 0.2);
  border-color: #ffd700;
  color: #ffd700;
  box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.rarity-badge.白银 {
  background: rgba(192, 192, 192, 0.2);
  border-color: #c0c0c0;
  color: #c0c0c0;
  box-shadow: 0 0 10px rgba(192, 192, 192, 0.3);
}

.weapon-name {
  font-size: 1.5rem;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.weapon-description {
  color: #cccccc;
  line-height: 1.6;
  font-size: 1rem;
}

.detail-footer {
  text-align: center;
  padding-top: 10px;
  border-top: 1px solid rgba(255, 165, 0, 0.3);
}

.close-detail-btn {
  background: linear-gradient(to bottom, #ffa500 0%, #ff8c00 100%);
  color: #000000;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.3);
}

.close-detail-btn:hover {
  background: linear-gradient(to bottom, #ffb84d 0%, #ffa500 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 165, 0, 0.4);
}

.close-detail-btn .icon {
  margin-right: 8px;
  font-size: 1.2rem;
}

.result-footer {
  text-align: center;
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-top: 15px;
  padding-top: 10px;
}

.flip-all-btn {
  background: linear-gradient(135deg, #ffa500, #ff8c00);
  border: none;
  color: white;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.flip-all-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(255, 165, 0, 0.4);
}

.close-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  border: none;
  color: white;
  padding: 12px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1.1rem;
  transition: all 0.3s ease;
}

.close-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.4);
}

@media (max-width: 768px) {
  .draw-section {
    bottom: 40px;
    left: 20px;
  }

  .character-section {
    right: 20px;
  }

  .draw-title {
    font-size: 2rem;
  }

  .character {
    max-height: 250px;
    max-width: 200px;
  }

  .result-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 12px;
    max-width: 95vw;
    padding: 10px;
  }

  .result-card {
    border-radius: 6px;
    min-width: 60px;
    min-height: 60px;
  }

  .result-modal {
    padding: 20px;
    width: 95vw;
    height: 85vh;
  }

  /* 移动端详情弹窗 */
  .card-detail-modal {
    width: 95vw;
    padding: 20px;
  }

  .detail-content {
    flex-direction: column;
    gap: 20px;
  }

  .weapon-image {
    flex: none;
  }

  .weapon-img {
    width: 150px;
    height: 150px;
  }

  .detail-header h3 {
    font-size: 1.5rem;
  }

  .weapon-name {
    font-size: 1.3rem;
  }
}
</style>
