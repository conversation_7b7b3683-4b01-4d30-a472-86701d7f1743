<template>
  <div class="ranking-bg">
    <div class="ranking-container">
      <!-- 游戏风格标题栏 -->
      <div class="ranking-header">
        <div class="header-title">
          <img src="/Unknown-5.png" alt="PUBG Logo" class="header-logo">
          <h1>积分排行榜</h1>
        </div>
        <div class="header-divider"></div>
      </div>

      <div class="ranking-content">
        <div class="pane-header">
          <h2 class="pane-title">战场积分榜</h2>
          <div class="military-subtitle">强者如云，谁与争锋</div>
        </div>
        
        <div class="ranking-table-container">
          <table class="ranking-table">
            <thead>
              <tr>
                <th>名次</th>
                <th>战士昵称</th>
                <th>战场积分</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, idx) in rankingList" :key="item.nickname" :class="getRowClass(idx)">
                <td>
                  <div class="rank-number">{{ idx + 1 }}</div>
                </td>
                <td>{{ item.nickname }}</td>
                <td>
                  <span class="score-value">{{ item.score }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Ranking',
  data() {
    return {
      rankingList: [
        { nickname: '绝地求生-小明', score: 120 },
        { nickname: '吃鸡王者', score: 110 },
        { nickname: '钢枪达人', score: 105 },
        { nickname: '伏地魔', score: 98 },
        { nickname: '狙神', score: 95 },
        { nickname: '步枪手', score: 90 },
        { nickname: '老司机', score: 88 },
        { nickname: '萌新', score: 80 },
      ]
    }
  },
  methods: {
    getRowClass(index) {
      if (index === 0) return 'rank-first';
      if (index === 1) return 'rank-second';
      if (index === 2) return 'rank-third';
      return '';
    }
  }
}
</script>

<style scoped>
/* 基础样式 */
.ranking-bg {
  display: flex;
  justify-content: center;
  padding: 20px 24px;
  padding-top: 100px;
  box-sizing: border-box;
  background-image: url('/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  position: relative;
  overflow-x: hidden;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  inset: 0;
  pointer-events: none;
  z-index: 1;
}

.military-border {
  position: absolute;
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5c242, transparent);
  left: 20px;
  right: 20px;
}

.military-border.top {
  top: 20px;
}

.military-border.bottom {
  bottom: 20px;
}

.corner-decoration {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid #f5c242;
}

.corner-decoration.top-left {
  top: 20px;
  left: 20px;
  border-right: none;
  border-bottom: none;
}

.corner-decoration.top-right {
  top: 20px;
  right: 20px;
  border-left: none;
  border-bottom: none;
}

.corner-decoration.bottom-left {
  bottom: 20px;
  left: 20px;
  border-right: none;
  border-top: none;
}

.corner-decoration.bottom-right {
  bottom: 20px;
  right: 20px;
  border-left: none;
  border-top: none;
}

/* 主容器 */
.ranking-container {
  width: 100%;
  max-width: 1000px;
  background: rgba(18, 22, 30, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 2px solid rgba(245, 194, 66, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5),
              inset 0 0 20px rgba(245, 194, 66, 0.1);
  position: relative;
  z-index: 2;
}

/* 头部标题 */
.ranking-header {
  padding: 20px 32px 0;
  text-align: center;
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.header-logo {
  height: 40px;
  filter: drop-shadow(0 0 8px rgba(245, 194, 66, 0.6));
}

.header-title h1 {
  font-family: 'Arial Black', sans-serif;
  font-size: 1.8rem;
  color: #f5c242;
  text-shadow: 0 0 8px rgba(245, 194, 66, 0.6),
               0 2px 4px rgba(0, 0, 0, 0.8);
  margin: 0;
}

.header-divider {
  height: 2px;
  background: linear-gradient(90deg, transparent, #f5c242, transparent);
  margin: 0 auto;
  width: 200px;
}

/* 内容区域 */
.ranking-content {
  padding: 24px 32px 32px;
}

.pane-header {
  text-align: center;
  margin-bottom: 30px;
}

.pane-title {
  font-family: 'Arial Black', sans-serif;
  font-size: 1.6rem;
  font-weight: 600;
  color: #f5c242;
  text-shadow: 0 0 8px rgba(245, 194, 66, 0.6);
  margin: 0 0 8px 0;
}

.military-subtitle {
  color: #b8860b;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 排行榜表格 */
.ranking-table-container {
  background: rgba(33, 36, 40, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(245, 194, 66, 0.2);
  overflow: hidden;
}

.ranking-table {
  width: 100%;
  border-collapse: collapse;
  color: #b8860b;
}

.ranking-table th, .ranking-table td {
  padding: 16px;
  text-align: center;
  border-bottom: 1px solid rgba(245, 194, 66, 0.2);
}

.ranking-table th {
  color: #f5c242;
  font-size: 1rem;
  font-weight: 600;
  background: rgba(245, 194, 66, 0.1);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.ranking-table tbody tr:last-child td {
  border-bottom: none;
}

.ranking-table tbody tr:hover {
  background: rgba(245, 194, 66, 0.05);
}

.rank-number {
  display: inline-block;
  width: 30px;
  height: 30px;
  line-height: 30px;
  border-radius: 50%;
  font-weight: bold;
  font-size: 1.1rem;
}

.rank-first .rank-number {
  background: linear-gradient(45deg, #ffd700, #ffed4e);
  color: #000;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.rank-second .rank-number {
  background: linear-gradient(45deg, #c0c0c0, #e5e5e5);
  color: #000;
  box-shadow: 0 0 10px rgba(192, 192, 192, 0.5);
}

.rank-third .rank-number {
  background: linear-gradient(45deg, #cd7f32, #daa520);
  color: #fff;
  box-shadow: 0 0 10px rgba(205, 127, 50, 0.5);
}

.rank-first td {
  color: #ffd700;
  font-weight: bold;
  font-size: 1.1rem;
}

.rank-second td {
  color: #c0c0c0;
  font-weight: bold;
}

.rank-third td {
  color: #cd7f32;
  font-weight: bold;
}

.score-value {
  font-weight: bold;
  font-size: 1.1rem;
  color: #f5c242;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .ranking-bg { 
    padding: 10px; 
  }
  
  .ranking-content { 
    padding: 20px 16px; 
  }
  
  .ranking-table th, .ranking-table td {
    padding: 12px 8px;
    font-size: 0.9rem;
  }
  
  .header-title h1 {
    font-size: 1.4rem;
  }
  
  .header-logo {
    height: 30px;
  }
}
</style> 