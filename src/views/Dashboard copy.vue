<template>
  <div class="dashboard-bg">
    <div class="dashboard-container">
      <div class="dashboard-tabs">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          :class="['tab-item', { active: currentTab === tab.key }]"
          @click="currentTab = tab.key"
        >
          {{ tab.label }}
        </div>
      </div>
      <div class="dashboard-content">
        <!-- 抽卡 Tab -->
        <div v-if="currentTab === 'draw'" class="tab-pane">
          <h1 class="pane-title">选择卡包类型</h1>
          <!-- <div class="rarity-prob-row">
            <span class="rarity-prob-tag normal">普通: 70%</span>
            <span class="rarity-prob-tag rare">稀有: 20%</span>
            <span class="rarity-prob-tag epic">史诗: 8%</span>
            <span class="rarity-prob-tag legendary">传说: 2%</span>
          </div> -->
      <div class="box-list">
        <div class="box-card" v-for="box in boxes" :key="box.type">
          <div class="box-img-wrap">
            <img :src="box.img" class="box-img" alt="box" />
          </div>
          <div class="box-info">
            <div class="box-title">{{ box.name }}</div>
            <div class="box-desc">{{ box.desc }}</div>
            <div class="box-meta">
              <span class="box-key">{{ box.price }} 钥匙</span>
                  <div class="draw-btn-group">
                    <button class="draw-btn" @click="drawOnce(box)">开始抽卡</button>
                    <button class="draw-btn ten" @click="drawTen(box)">10连抽</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 我的收藏 Tab -->
        <div v-else-if="currentTab === 'collection'" class="tab-pane">
          <h1 class="pane-title">我的卡牌收藏</h1>
          <div class="custom-filters">
            <div class="filter-group">
              <span class="filter-label">稀有度：</span>
              <button
                v-for="rarity in ['all', ...rarityOptions]"
                :key="rarity"
                :class="['filter-btn', { active: rarityFilter === rarity }]"
                @click="rarityFilter = rarity"
              >
                {{ rarity === 'all' ? '全部' : rarity }}
              </button>
            </div>
            <div class="filter-group">
              <span class="filter-label">类型：</span>
              <button
                v-for="type in ['all', ...typeOptions]"
                :key="type"
                :class="['filter-btn', { active: typeFilter === type }]"
                @click="typeFilter = type"
              >
                {{ type === 'all' ? '全部' : type }}
              </button>
            </div>
          </div>
          <div class="collection-grid">
            <div
              v-for="card in filteredCollection"
              :key="card.id"
              class="collection-card"
              :style="{ 'border-color': rarityLevels[card.rarity]?.color || '#455A64' }"
            >
              <div class="collection-card-img-wrap">
                <img :src="card.img" :alt="card.name" class="collection-card-img" />
              </div>
              <div class="collection-card-info">
                <div class="collection-card-name">{{ card.name }}</div>
                <div class="collection-card-type">{{ card.type }}</div>
                <span class="rarity-tag" :style="{ 'background-color': rarityLevels[card.rarity]?.color || '#455A64' }">
                  {{ card.rarity }}
                </span>
          </div>
        </div>
      </div>
        </div>

        <!-- 抽卡记录 Tab -->
        <div v-else-if="currentTab === 'history'" class="tab-pane">
          <h1 class="pane-title">抽卡历史记录</h1>
          <div class="history-table-container">
            <table class="history-table">
              <thead>
                <tr>
                  <th>时间</th>
                  <th>卡包</th>
                  <th>获得卡牌</th>
                  <th>稀有度</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in drawHistory" :key="index">
                  <td>{{ item.time }}</td>
                  <td>{{ item.pack }}</td>
                  <td>{{ item.cardName }}</td>
                  <td>
                    <span class="rarity-tag" :style="{ 'background-color': rarityLevels[item.rarity].color || '#455A64' }">
                      {{ item.rarity }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 统计信息 Tab -->
        <div v-else-if="currentTab === 'stats'" class="tab-pane">
          <h1 class="pane-title">抽卡统计</h1>
          <div class="stats-summary">
            <div class="stat-card">
              <div class="stat-value">{{ statistics.totalDraws }}</div>
              <div class="stat-label">总抽卡次数</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ statistics.totalCollection }}</div>
              <div class="stat-label">总收藏数</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ statistics.legendaryCount }}</div>
              <div class="stat-label">传说卡牌</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ statistics.epicCount }}</div>
              <div class="stat-label">史诗卡牌</div>
            </div>
          </div>
          <div class="stats-distribution">
            <h2 class="distribution-title">稀有度分布</h2>
            <div class="distribution-chart">
              <div v-for="item in statistics.rarityDistribution" :key="item.rarity" class="dist-bar-group">
                <span class="dist-label">{{ item.rarity }}</span>
                <div class="dist-bar-bg">
                  <div 
                    class="dist-bar-fg" 
                    :style="{ 
                      width: `${(item.count / statistics.totalDraws) * 100}%`,
                      'background-color': rarityLevels[item.rarity].color || '#455A64' 
                    }"
                  ></div>
                </div>
                <span class="dist-count">{{ item.count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showDrawModal" class="draw-fullscreen-overlay">
      <video 
        ref="drawVideo"
        :src="currentDrawVideo" 
        class="draw-video-fullscreen" 
        @ended="onVideoEnded"
        autoplay
      ></video>
      <div class="draw-cards-container" :class="{'cards-visible': cardsVisible}">
        <h2 class="draw-result-title">抽卡结果</h2>
        <div class="draw-cards-row">
          <CardFlip
            v-for="(card, idx) in drawResultCards"
            :key="card._id"
            :isFlipped="showFlippedCards"
            :img="card.img"
            :name="card.name"
            :type="card.type"
            :rarity="card.rarity"
            :rarityColor="rarityLevels[card.rarity].color || '#607D8B'"
            :backImg="'/kpkp.png'"
          />
        </div>
        <button class="draw-close-btn" @click="closeDrawModal">关闭</button>
      </div>
    </div>
  </div>
</template>

<script>
import CardFlip from '@/components/CardFlip.vue'
export default {
  name: 'Dashboard',
  components: { CardFlip },
  data() {
    return {
      currentTab: 'draw',
      tabs: [
        { key: 'draw', label: '抽卡' },
        { key: 'collection', label: '我的收藏' },
        { key: 'history', label: '抽卡记录' },
        { key: 'stats', label: '统计信息' },
      ],
      boxes: [
        { type: 'standard', name: '标准包', desc: '包含所有类型', price: 1, img: '/box_luxury.png' }
      ],
      rarityLevels: {
        '普通': { color: '#607D8B' }, // Blue Grey
        '稀有': { color: '#4CAF50' }, // Green
        '史诗': { color: '#9C27B0' }, // Purple
        '传说': { color: '#FF9800' }, // Orange
      },
      myCollection: [
        { id: 1, name: 'AKM', type: '武器', rarity: '普通', img: '/box_weapon.png' }, // Placeholder image
        { id: 2, name: 'M416', type: '武器', rarity: '稀有', img: '/box_weapon.png' }, // Placeholder image
        { id: 3, name: 'AWM', type: '武器', rarity: '传说', img: '/box_luxury.png' }, // Placeholder image
        { id: 4, name: '三级头', type: '装备', rarity: '史诗', img: '/box_equip.png' }, // Placeholder image
      ],
      drawHistory: [
        { time: '2024-01-15 14:30:00', pack: '武器包', cardName: 'AKM', rarity: '普通' },
        { time: '2024-01-15 14:25:00', pack: '装备包', cardName: '三级头', rarity: '史诗' },
        { time: '2024-01-15 14:20:00', pack: '豪华包', cardName: 'AWM', rarity: '传说' },
      ],
      statistics: {
        totalDraws: 25,
        totalCollection: 18,
        legendaryCount: 2,
        epicCount: 5,
        rarityDistribution: [
          { rarity: '普通', count: 8 },
          { rarity: '稀有', count: 5 },
          { rarity: '史诗', count: 5 },
          { rarity: '传说', count: 2 },
        ]
      },
      rarityFilter: 'all',
      typeFilter: 'all',
      showDrawModal: false,
      drawResultCards: [],
      currentFlipIndex: -1,
      flipTimer: null,
      showDrawVideo: false,
      currentDrawVideo: '/yin.mp4',
      cardsVisible: false,
      showFlippedCards: false,
    }
  },
  computed: {
    filteredCollection() {
      return this.myCollection.filter(card => {
        const rarityMatch = this.rarityFilter === 'all' || card.rarity === this.rarityFilter;
        const typeMatch = this.typeFilter === 'all' || card.type === this.typeFilter;
        return rarityMatch && typeMatch;
      });
    },
    rarityOptions() {
      return [...new Set(this.myCollection.map(c => c.rarity))];
    },
    typeOptions() {
      return [...new Set(this.myCollection.map(c => c.type))];
    }
  },
  methods: {
    drawOnce(box) {
      this.startDraw(1, box)
    },
    drawTen(box) {
      this.startDraw(10, box)
    },
    async startDraw(count) {
  // 重置弹窗状态
  this.showDrawModal = false;
  this.cardsVisible = false;
  this.flippedCardIndexes = [];
  
  // 短暂延迟确保DOM更新
  await this.$nextTick();
  
  // 模拟请求后台获取抽卡结果和特效类型
  const result = await fetchDrawResult({ count });
  this.drawResultCards = result.cards;
  this.effectType = result.effect;
  this.currentDrawVideo = result.effect === 'gold' ? '/huang.mp4' : '/yin.mp4';
  this.showDrawModal = true;
  this.lastDrawCount = count;
},
    autoFlipCards() {
      if (this.flipTimer) clearTimeout(this.flipTimer)
      let idx = 0
      const flipNext = () => {
        if (idx < this.drawResultCards.length) {
          this.currentFlipIndex = idx
          idx++
          this.flipTimer = setTimeout(flipNext, 500)
        }
      }
      // 先延迟300ms再翻第一个
      this.flipTimer = setTimeout(flipNext, 300)
    },
    closeDrawModal() {
      if (this.flipTimer) clearTimeout(this.flipTimer)
      // 把抽到的卡牌加入收藏和历史
      this.myCollection.push(...this.drawResultCards)
      const now = new Date()
      for (const card of this.drawResultCards) {
        this.drawHistory.unshift({
          time: now.toLocaleString(),
          pack: '抽卡',
          cardName: card.name,
          rarity: card.rarity,
        })
      }
      // 更新统计
      this.statistics.totalDraws += this.drawResultCards.length
      this.statistics.totalCollection = this.myCollection.length
      for (const card of this.drawResultCards) {
        if (card.rarity === '传说') this.statistics.legendaryCount++
        if (card.rarity === '史诗') this.statistics.epicCount++
        const dist = this.statistics.rarityDistribution.find(r => r.rarity === card.rarity)
        if (dist) dist.count++
      }
      this.showDrawModal = false
      this.drawResultCards = []
      this.cardsVisible = false
      this.showFlippedCards = false
    },
    onVideoEnded() {
      // 视频播放完毕后，显示卡牌并设置延迟翻转
      this.cardsVisible = true
      
      // 等待卡牌移动动画完成后再翻转
      setTimeout(() => {
        this.showFlippedCards = true
      }, 800); // 800ms等待卡牌从底部移动到位
    }
  }
}
</script>

<style scoped>
/* General */
.dashboard-bg {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  padding: 40px 24px;
  background-color: #1a1a1a;
  background-image: url('/background.jpg'); /* Make sure you have a background.jpg in /public */
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.dashboard-container {
  width: 100%;
  max-width: 1400px;
  background: rgba(21, 23, 24, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Tabs */
.dashboard-tabs {
  display: flex;
  padding: 0 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-item {
  padding: 18px 24px;
  cursor: pointer;
  color: #a9b1bb;
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  transition: color 0.3s;
}

.tab-item:hover {
  color: #ffffff;
}

.tab-item.active {
  color: #ffffff;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #00aeff;
  border-radius: 3px 3px 0 0;
}

/* Content Pane */
.dashboard-content {
  padding: 32px;
}

.pane-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #00aeff;
  margin-bottom: 24px;
}

/* Draw Tab */
.box-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.box-card {
  background: rgba(33, 36, 40, 0.7);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.box-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 174, 255, 0.2);
}

.box-img-wrap {
  height: 160px;
  background-color: #111;
  display: flex;
  align-items: center;
  justify-content: center;
}

.box-img {
  max-height: 90%;
  max-width: 90%;
  object-fit: contain;
}

.box-info {
  padding: 16px;
}

.box-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8px;
}

.box-desc {
  font-size: 0.9rem;
  color: #a9b1bb;
  margin-bottom: 16px;
  min-height: 40px;
}

.box-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-key {
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffc107;
}

.draw-btn-group {
  display: flex;
  gap: 10px;
}
.draw-btn {
  background: #00aeff;
  color: #fff;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}
.draw-btn:hover {
  background: #0095cc;
}
.draw-btn.ten {
  background: #2196f3;
}
.draw-btn.ten:hover {
  background: #1769aa;
}

/* Collection Tab */
.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.filter-select {
  background: rgba(33, 36, 40, 0.7);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 1rem;
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 24px;
}

.collection-card {
  background: rgba(33, 36, 40, 0.7);
  border-radius: 8px;
  border: 2px solid; /* color set dynamically */
  padding: 16px;
  text-align: center;
}

.collection-card-img-wrap {
  height: 120px;
  margin-bottom: 16px;
}

.collection-card-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.collection-card-name {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
}

.collection-card-type {
  color: #a9b1bb;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.rarity-tag {
  color: #fff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-block;
}

/* History Tab */
.history-table-container {
  overflow-x: auto;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
  color: #a9b1bb;
}

.history-table th, .history-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-table th {
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
}

.history-table tbody tr:last-child td {
  border-bottom: none;
}

/* Stats Tab */
.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: rgba(33, 36, 40, 0.7);
  padding: 24px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #00aeff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1rem;
  color: #a9b1bb;
}

.distribution-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 24px;
}

.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dist-bar-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dist-label {
  flex: 0 0 80px;
  color: #a9b1bb;
}

.dist-bar-bg {
  flex-grow: 1;
  background: rgba(33, 36, 40, 0.7);
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.dist-bar-fg {
  height: 100%;
  border-radius: 10px;
}

.dist-count {
  flex: 0 0 40px;
  text-align: right;
  color: #fff;
  font-weight: 500;
}

/* 自定义筛选按钮组 */
.custom-filters {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}
.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}
.filter-label {
  color: #a9b1bb;
  font-size: 1rem;
  margin-right: 4px;
}
.filter-btn {
  background: rgba(33, 36, 40, 0.7);
  color: #fff;
  border: 1.5px solid rgba(255,255,255,0.12);
  border-radius: 18px;
  padding: 6px 18px;
  font-size: 1rem;
  margin-right: 4px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
  outline: none;
}
.filter-btn.active {
  background: #00aeff;
  color: #fff;
  border: 1.5px solid #00aeff;
  font-weight: 600;
  box-shadow: 0 2px 8px #00aeff33;
}
.filter-btn:last-child {
  margin-right: 0;
}

.rarity-prob-row {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  margin-top: -8px;
}
.rarity-prob-tag {
  display: inline-block;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 16px;
  padding: 4px 18px;
  background: #888;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.rarity-prob-tag.normal {
  background: #888;
}
.rarity-prob-tag.rare {
  background: #2196f3;
}
.rarity-prob-tag.epic {
  background: #9c27b0;
}
.rarity-prob-tag.legendary {
  background: #ff9800;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-bg { padding: 16px; }
  .dashboard-tabs { padding: 0 16px; }
  .tab-item { padding: 16px 12px; font-size: 0.9rem; }
  .dashboard-content { padding: 24px 16px; }
  .pane-title { font-size: 1.5rem; }
  .filters { flex-direction: column; }
  .box-list, .collection-grid, .stats-summary {
    grid-template-columns: 1fr;
  }
  .custom-filters { flex-direction: column; gap: 12px; }
  .rarity-prob-row { flex-wrap: wrap; gap: 8px; }
  .rarity-prob-tag { font-size: 0.95rem; padding: 4px 12px; }
}
.draw-modal-bg {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.55);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  overflow: auto;
}
.draw-modal {
  background: #23262e;
  border-radius: 16px;
  box-shadow: 0 4px 24px #000a;
  padding: 32px 24px 24px 24px;
  min-width: 320px;
  max-width: 90vw;
  width: fit-content;
  text-align: center;
  max-height: 90vh;
  overflow-y: auto;
}
.draw-modal-title {
  color: #00aeff;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 18px;
}
.draw-cards-row {
  display: flex;
  gap: 18px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 18px;
}
.draw-modal-close {
  background: #00aeff;
  color: #fff;
  border: none;
  padding: 8px 28px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
}
.draw-modal-close:hover {
  background: #0095cc;
}
@media (max-width: 600px) {
  .draw-modal { padding: 16px 4vw 12px 4vw; min-width: 0; }
  .draw-cards-row { gap: 8px; }
}
.draw-video {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
  border-radius: 8px;
}
.video-cards-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
  border-radius: 8px;
  overflow: hidden;
}

.draw-video-bg {
  width: 100%;
  display: block;
}

.draw-cards-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 18px;
  padding: 20px;
  z-index: 1;
}

@media (max-width: 600px) {
  .draw-cards-overlay {
    gap: 8px;
    padding: 10px;
  }
}

.draw-fullscreen-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  background-color: #000;
  overflow: hidden;
}

.draw-video-fullscreen {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1001;
}

.draw-cards-container {
  position: absolute;
  inset: 0;
  z-index: 1002;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  transform: translateY(100%);
  opacity: 0;
  transition: transform 0.8s cubic-bezier(0.17, 0.89, 0.32, 1.25), opacity 0.8s ease;
}

.draw-cards-container.cards-visible {
  transform: translateY(0);
  opacity: 1;
}

.draw-result-title {
  color: #00aeff;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(0,0,0,0.8);
  margin-bottom: 30px;
}

.draw-cards-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 18px;
  margin-bottom: 30px;
}

.draw-close-btn {
  background: #00aeff;
  color: #fff;
  border: none;
  padding: 12px 36px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.draw-close-btn:hover {
  background: #0095cc;
}

@media (max-width: 600px) {
  .draw-cards-row {
    gap: 10px;
  }
  
  .draw-result-title {
    font-size: 1.4rem;
    margin-bottom: 20px;
  }
}
</style>

<template>
  <div class="dashboard-container">
    <!-- 军事风格背景 -->
    <div class="battlefield-bg">
      <div class="bg-overlay"></div>
      <video autoplay loop muted playsinline class="bg-video">
        <source src="/bg_fixed2_1080p.m3u8" type="application/x-mpegURL">
      </video>
    </div>

    <!-- 军事风格面板 -->
    <div class="military-panel">
      <!-- 标签页导航 - 改为军事风格选项卡 -->
      <div class="military-tabs">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          :class="['tab-button', { 'active': currentTab === tab.key }]"
          @click="currentTab = tab.key"
        >
          <i class="tab-icon">{{ tab.icon }}</i>
          <span class="tab-label">{{ tab.label }}</span>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="content-area">
        <!-- 抽卡 Tab - 军事风格卡包选择 -->
        <div v-if="currentTab === 'draw' class="tab-content">
          <div class="military-header">
            <h1 class="section-title">战场补给箱</h1>
            <div class="key-counter">
              <img src="/jb.png" alt="Key Icon" class="key-icon">
              <span class="key-value">99</span>
            </div>
          </div>

          <!-- 卡包展示 - 3D军事补给箱风格 -->
          <div class="supply-crates">
            <div v-for="box in boxes" :key="box.type" class="supply-crate">
              <div class="crate-container">
                <div class="crate-3d">
                  <img :src="box.img" alt="{{ box.name }}" class="crate-img">
                  <div class="crate-overlay"></div>
                  <div class="crate-rarity-badge">{{ box.rarity }}</div>
                </div>
                <div class="crate-info">
                  <h3 class="crate-name">{{ box.name }}</h3>
                  <p class="crate-desc">{{ box.desc }}</p>
                  <div class="crate-actions">
                    <button class="action-btn single-draw" @click="drawOnce(box)">
                      <span class="cost">{{ box.price }} 钥匙</span>
                      <span class="action-text">单次抽取</span>
                    </button>
                    <button class="action-btn multi-draw" @click="drawTen(box)">
                      <span class="cost">{{ box.price * 10 }} 钥匙</span>
                      <span class="action-text">十连抽</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 其他标签页内容保持结构不变，样式同步更新 -->
        <div v-else-if="currentTab === 'collection'" class="tab-pane">
          <h1 class="pane-title">我的卡牌收藏</h1>
          <div class="custom-filters">
            <div class="filter-group">
              <span class="filter-label">稀有度：</span>
              <button
                v-for="rarity in ['all', ...rarityOptions]"
                :key="rarity"
                :class="['filter-btn', { active: rarityFilter === rarity }]"
                @click="rarityFilter = rarity"
              >
                {{ rarity === 'all' ? '全部' : rarity }}
              </button>
            </div>
            <div class="filter-group">
              <span class="filter-label">类型：</span>
              <button
                v-for="type in ['all', ...typeOptions]"
                :key="type"
                :class="['filter-btn', { active: typeFilter === type }]"
                @click="typeFilter = type"
              >
                {{ type === 'all' ? '全部' : type }}
              </button>
            </div>
          </div>
          <div class="collection-grid">
            <div
              v-for="card in filteredCollection"
              :key="card.id"
              class="collection-card"
              :style="{ 'border-color': rarityLevels[card.rarity]?.color || '#455A64' }"
            >
              <div class="collection-card-img-wrap">
                <img :src="card.img" :alt="card.name" class="collection-card-img" />
              </div>
              <div class="collection-card-info">
                <div class="collection-card-name">{{ card.name }}</div>
                <div class="collection-card-type">{{ card.type }}</div>
                <span class="rarity-tag" :style="{ 'background-color': rarityLevels[card.rarity]?.color || '#455A64' }">
                  {{ card.rarity }}
                </span>
          </div>
        </div>
      </div>
        </div>

        <!-- 抽卡记录 Tab -->
        <div v-else-if="currentTab === 'history'" class="tab-pane">
          <h1 class="pane-title">抽卡历史记录</h1>
          <div class="history-table-container">
            <table class="history-table">
              <thead>
                <tr>
                  <th>时间</th>
                  <th>卡包</th>
                  <th>获得卡牌</th>
                  <th>稀有度</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(item, index) in drawHistory" :key="index">
                  <td>{{ item.time }}</td>
                  <td>{{ item.pack }}</td>
                  <td>{{ item.cardName }}</td>
                  <td>
                    <span class="rarity-tag" :style="{ 'background-color': rarityLevels[item.rarity]?.color || '#455A64' }">
                      {{ item.rarity }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 统计信息 Tab -->
        <div v-else-if="currentTab === 'stats'" class="tab-pane">
          <h1 class="pane-title">抽卡统计</h1>
          <div class="stats-summary">
            <div class="stat-card">
              <div class="stat-value">{{ statistics.totalDraws }}</div>
              <div class="stat-label">总抽卡次数</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ statistics.totalCollection }}</div>
              <div class="stat-label">总收藏数</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ statistics.legendaryCount }}</div>
              <div class="stat-label">传说卡牌</div>
            </div>
            <div class="stat-card">
              <div class="stat-value">{{ statistics.epicCount }}</div>
              <div class="stat-label">史诗卡牌</div>
            </div>
          </div>
          <div class="stats-distribution">
            <h2 class="distribution-title">稀有度分布</h2>
            <div class="distribution-chart">
              <div v-for="item in statistics.rarityDistribution" :key="item.rarity" class="dist-bar-group">
                <span class="dist-label">{{ item.rarity }}</span>
                <div class="dist-bar-bg">
                  <div 
                    class="dist-bar-fg" 
                    :style="{ 
                      width: `${(item.count / statistics.totalDraws) * 100}%`,
                      'background-color': rarityLevels[item.rarity]?.color || '#455A64' 
                    }"
                  ></div>
                </div>
                <span class="dist-count">{{ item.count }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showDrawModal" class="draw-fullscreen-overlay">
      <video 
        ref="drawVideo"
        :src="currentDrawVideo" 
        class="draw-video-fullscreen" 
        @ended="onVideoEnded"
        autoplay
      ></video>
      <div class="draw-cards-container" :class="{'cards-visible': cardsVisible}">
        <h2 class="draw-result-title">抽卡结果</h2>
        <div class="draw-cards-row">
          <CardFlip
            v-for="(card, idx) in drawResultCards"
            :key="card._id"
            :isFlipped="showFlippedCards"
            :img="card.img"
            :name="card.name"
            :type="card.type"
            :rarity="card.rarity"
            :rarityColor="rarityLevels[card.rarity]?.color || '#607D8B'"
            :backImg="'/kpkp.png'"
          />
        </div>
        <button class="draw-close-btn" @click="closeDrawModal">关闭</button>
      </div>
    </div>
  </div>
</template>

<script>
import CardFlip from '@/components/CardFlip.vue'
export default {
  name: 'Dashboard',
  components: { CardFlip },
  data() {
    return {
      currentTab: 'draw',
      tabs: [
        { key: 'draw', label: '抽卡' },
        { key: 'collection', label: '我的收藏' },
        { key: 'history', label: '抽卡记录' },
        { key: 'stats', label: '统计信息' },
      ],
      boxes: [
        { type: 'standard', name: '标准包', desc: '包含所有类型', price: 1, img: '/box_luxury.png' }
      ],
      rarityLevels: {
        '普通': { color: '#607D8B' }, // Blue Grey
        '稀有': { color: '#4CAF50' }, // Green
        '史诗': { color: '#9C27B0' }, // Purple
        '传说': { color: '#FF9800' }, // Orange
      },
      myCollection: [
        { id: 1, name: 'AKM', type: '武器', rarity: '普通', img: '/box_weapon.png' }, // Placeholder image
        { id: 2, name: 'M416', type: '武器', rarity: '稀有', img: '/box_weapon.png' }, // Placeholder image
        { id: 3, name: 'AWM', type: '武器', rarity: '传说', img: '/box_luxury.png' }, // Placeholder image
        { id: 4, name: '三级头', type: '装备', rarity: '史诗', img: '/box_equip.png' }, // Placeholder image
      ],
      drawHistory: [
        { time: '2024-01-15 14:30:00', pack: '武器包', cardName: 'AKM', rarity: '普通' },
        { time: '2024-01-15 14:25:00', pack: '装备包', cardName: '三级头', rarity: '史诗' },
        { time: '2024-01-15 14:20:00', pack: '豪华包', cardName: 'AWM', rarity: '传说' },
      ],
      statistics: {
        totalDraws: 25,
        totalCollection: 18,
        legendaryCount: 2,
        epicCount: 5,
        rarityDistribution: [
          { rarity: '普通', count: 8 },
          { rarity: '稀有', count: 5 },
          { rarity: '史诗', count: 5 },
          { rarity: '传说', count: 2 },
        ]
      },
      rarityFilter: 'all',
      typeFilter: 'all',
      showDrawModal: false,
      drawResultCards: [],
      currentFlipIndex: -1,
      flipTimer: null,
      showDrawVideo: false,
      currentDrawVideo: '/yin.mp4',
      cardsVisible: false,
      showFlippedCards: false,
    }
  },
  computed: {
    filteredCollection() {
      return this.myCollection.filter(card => {
        const rarityMatch = this.rarityFilter === 'all' || card.rarity === this.rarityFilter;
        const typeMatch = this.typeFilter === 'all' || card.type === this.typeFilter;
        return rarityMatch && typeMatch;
      });
    },
    rarityOptions() {
      return [...new Set(this.myCollection.map(c => c.rarity))];
    },
    typeOptions() {
      return [...new Set(this.myCollection.map(c => c.type))];
    }
  },
  methods: {
    drawOnce(box) {
      this.startDraw(1, box)
    },
    drawTen(box) {
      this.startDraw(10, box)
    },
    startDraw(count, box) {
      // 随机生成count张卡牌
      const pool = [
        { name: 'AKM', type: '武器', img: '/box_weapon.png' },
        { name: 'M416', type: '武器', img: '/box_weapon.png' },
        { name: 'AWM', type: '武器', img: '/box_luxury.png' },
        { name: '三级头', type: '装备', img: '/box_equip.png' },
        { name: '吉利服', type: '装备', img: '/box_equip.png' },
        { name: '特种兵', type: '角色', img: '/box_role.png' },
        { name: '女特工', type: '角色', img: '/box_role.png' },
      ]
      const rarityArr = [
        { rarity: '普通', prob: 70 },
        { rarity: '稀有', prob: 20 },
        { rarity: '史诗', prob: 8 },
        { rarity: '传说', prob: 2 },
      ]
      function randomRarity() {
        const r = Math.random() * 100
        let sum = 0
        for (const item of rarityArr) {
          sum += item.prob
          if (r < sum) return item.rarity
        }
        return '普通'
      }
      const cards = []
      for (let i = 0; i < count; i++) {
        const base = pool[Math.floor(Math.random() * pool.length)]
        const rarity = randomRarity()
        cards.push({
          _id: Date.now() + '-' + i + '-' + Math.random(),
          ...base,
          rarity,
        })
      }
      this.drawResultCards = cards
      
      // 检查是否有史诗卡牌
      const hasEpic = cards.some(card => card.rarity === '史诗' || card.rarity === '传说')
      this.currentDrawVideo = hasEpic ? '/huang.mp4' : '/yin.mp4'
      
      this.showDrawModal = true
      this.cardsVisible = false
      this.showFlippedCards = false
      // 不再调用autoFlipCards，改为在视频结束后触发
    },
    autoFlipCards() {
      if (this.flipTimer) clearTimeout(this.flipTimer)
      let idx = 0
      const flipNext = () => {
        if (idx < this.drawResultCards.length) {
          this.currentFlipIndex = idx
          idx++
          this.flipTimer = setTimeout(flipNext, 500)
        }
      }
      // 先延迟300ms再翻第一个
      this.flipTimer = setTimeout(flipNext, 300)
    },
    closeDrawModal() {
      if (this.flipTimer) clearTimeout(this.flipTimer)
      // 把抽到的卡牌加入收藏和历史
      this.myCollection.push(...this.drawResultCards)
      const now = new Date()
      for (const card of this.drawResultCards) {
        this.drawHistory.unshift({
          time: now.toLocaleString(),
          pack: '抽卡',
          cardName: card.name,
          rarity: card.rarity,
        })
      }
      // 更新统计
      this.statistics.totalDraws += this.drawResultCards.length
      this.statistics.totalCollection = this.myCollection.length
      for (const card of this.drawResultCards) {
        if (card.rarity === '传说') this.statistics.legendaryCount++
        if (card.rarity === '史诗') this.statistics.epicCount++
        const dist = this.statistics.rarityDistribution.find(r => r.rarity === card.rarity)
        if (dist) dist.count++
      }
      this.showDrawModal = false
      this.drawResultCards = []
      this.cardsVisible = false
      this.showFlippedCards = false
    },
    onVideoEnded() {
      // 视频播放完毕后，显示卡牌并设置延迟翻转
      this.cardsVisible = true
      
      // 等待卡牌移动动画完成后再翻转
      setTimeout(() => {
        this.showFlippedCards = true
      }, 800); // 800ms等待卡牌从底部移动到位
    }
  }
}
</script>

<style scoped>
/* General */
.dashboard-bg {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  padding: 40px 24px;
  background-color: #1a1a1a;
  background-image: url('/background.jpg'); /* Make sure you have a background.jpg in /public */
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
}

.dashboard-container {
  width: 100%;
  max-width: 1400px;
  background: rgba(21, 23, 24, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Tabs */
.dashboard-tabs {
  display: flex;
  padding: 0 32px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.tab-item {
  padding: 18px 24px;
  cursor: pointer;
  color: #a9b1bb;
  font-size: 1rem;
  font-weight: 500;
  position: relative;
  transition: color 0.3s;
}

.tab-item:hover {
  color: #ffffff;
}

.tab-item.active {
  color: #ffffff;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: #00aeff;
  border-radius: 3px 3px 0 0;
}

/* Content Pane */
.dashboard-content {
  padding: 32px;
}

.pane-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #00aeff;
  margin-bottom: 24px;
}

/* Draw Tab */
.box-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 24px;
}

.box-card {
  background: rgba(33, 36, 40, 0.7);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.box-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 174, 255, 0.2);
}

.box-img-wrap {
  height: 160px;
  background-color: #111;
  display: flex;
  align-items: center;
  justify-content: center;
}

.box-img {
  max-height: 90%;
  max-width: 90%;
  object-fit: contain;
}

.box-info {
  padding: 16px;
}

.box-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 8px;
}

.box-desc {
  font-size: 0.9rem;
  color: #a9b1bb;
  margin-bottom: 16px;
  min-height: 40px;
}

.box-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-key {
  font-size: 1.1rem;
  font-weight: 700;
  color: #ffc107;
}

.draw-btn-group {
  display: flex;
  gap: 10px;
}
.draw-btn {
  background: #00aeff;
  color: #fff;
  border: none;
  padding: 8px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.3s;
}
.draw-btn:hover {
  background: #0095cc;
}
.draw-btn.ten {
  background: #2196f3;
}
.draw-btn.ten:hover {
  background: #1769aa;
}

/* Collection Tab */
.filters {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
}

.filter-select {
  background: rgba(33, 36, 40, 0.7);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  padding: 10px 16px;
  font-size: 1rem;
}

.collection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 24px;
}

.collection-card {
  background: rgba(33, 36, 40, 0.7);
  border-radius: 8px;
  border: 2px solid; /* color set dynamically */
  padding: 16px;
  text-align: center;
}

.collection-card-img-wrap {
  height: 120px;
  margin-bottom: 16px;
}

.collection-card-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.collection-card-name {
  color: #fff;
  font-size: 1.1rem;
  font-weight: 600;
}

.collection-card-type {
  color: #a9b1bb;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.rarity-tag {
  color: #fff;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  display: inline-block;
}

/* History Tab */
.history-table-container {
  overflow-x: auto;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
  color: #a9b1bb;
}

.history-table th, .history-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.history-table th {
  color: #fff;
  font-size: 1rem;
  font-weight: 600;
}

.history-table tbody tr:last-child td {
  border-bottom: none;
}

/* Stats Tab */
.stats-summary {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  background: rgba(33, 36, 40, 0.7);
  padding: 24px;
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #00aeff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 1rem;
  color: #a9b1bb;
}

.distribution-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #fff;
  margin-bottom: 24px;
}

.distribution-chart {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dist-bar-group {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dist-label {
  flex: 0 0 80px;
  color: #a9b1bb;
}

.dist-bar-bg {
  flex-grow: 1;
  background: rgba(33, 36, 40, 0.7);
  height: 20px;
  border-radius: 10px;
  overflow: hidden;
}

.dist-bar-fg {
  height: 100%;
  border-radius: 10px;
}

.dist-count {
  flex: 0 0 40px;
  text-align: right;
  color: #fff;
  font-weight: 500;
}

/* 自定义筛选按钮组 */
.custom-filters {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}
.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}
.filter-label {
  color: #a9b1bb;
  font-size: 1rem;
  margin-right: 4px;
}
.filter-btn {
  background: rgba(33, 36, 40, 0.7);
  color: #fff;
  border: 1.5px solid rgba(255,255,255,0.12);
  border-radius: 18px;
  padding: 6px 18px;
  font-size: 1rem;
  margin-right: 4px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, border 0.2s;
  outline: none;
}
.filter-btn.active {
  background: #00aeff;
  color: #fff;
  border: 1.5px solid #00aeff;
  font-weight: 600;
  box-shadow: 0 2px 8px #00aeff33;
}
.filter-btn:last-child {
  margin-right: 0;
}

.rarity-prob-row {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  margin-top: -8px;
}
.rarity-prob-tag {
  display: inline-block;
  color: #fff;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 16px;
  padding: 4px 18px;
  background: #888;
  box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}
.rarity-prob-tag.normal {
  background: #888;
}
.rarity-prob-tag.rare {
  background: #2196f3;
}
.rarity-prob-tag.epic {
  background: #9c27b0;
}
.rarity-prob-tag.legendary {
  background: #ff9800;
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-bg { padding: 16px; }
  .dashboard-tabs { padding: 0 16px; }
  .tab-item { padding: 16px 12px; font-size: 0.9rem; }
  .dashboard-content { padding: 24px 16px; }
  .pane-title { font-size: 1.5rem; }
  .filters { flex-direction: column; }
  .box-list, .collection-grid, .stats-summary {
    grid-template-columns: 1fr;
  }
  .custom-filters { flex-direction: column; gap: 12px; }
  .rarity-prob-row { flex-wrap: wrap; gap: 8px; }
  .rarity-prob-tag { font-size: 0.95rem; padding: 4px 12px; }
}
.draw-modal-bg {
  position: fixed;
  inset: 0;
  background: rgba(0,0,0,0.55);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: auto;
  overflow: auto;
}
.draw-modal {
  background: #23262e;
  border-radius: 16px;
  box-shadow: 0 4px 24px #000a;
  padding: 32px 24px 24px 24px;
  min-width: 320px;
  max-width: 90vw;
  width: fit-content;
  text-align: center;
  max-height: 90vh;
  overflow-y: auto;
}
.draw-modal-title {
  color: #00aeff;
  font-size: 1.4rem;
  font-weight: 600;
  margin-bottom: 18px;
}
.draw-cards-row {
  display: flex;
  gap: 18px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 18px;
}
.draw-modal-close {
  background: #00aeff;
  color: #fff;
  border: none;
  padding: 8px 28px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  margin-top: 8px;
  transition: background 0.2s;
}
.draw-modal-close:hover {
  background: #0095cc;
}
@media (max-width: 600px) {
  .draw-modal { padding: 16px 4vw 12px 4vw; min-width: 0; }
  .draw-cards-row { gap: 8px; }
}
.draw-video {
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
  border-radius: 8px;
}
.video-cards-container {
  position: relative;
  width: 100%;
  max-width: 800px;
  margin: 0 auto 24px;
  border-radius: 8px;
  overflow: hidden;
}

.draw-video-bg {
  width: 100%;
  display: block;
}

.draw-cards-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 18px;
  padding: 20px;
  z-index: 1;
}

@media (max-width: 600px) {
  .draw-cards-overlay {
    gap: 8px;
    padding: 10px;
  }
}

.draw-fullscreen-overlay {
  position: fixed;
  inset: 0;
  z-index: 1000;
  background-color: #000;
  overflow: hidden;
}

.draw-video-fullscreen {
  position: absolute;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 1001;
}

.draw-cards-container {
  position: absolute;
  inset: 0;
  z-index: 1002;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px;
  transform: translateY(100%);
  opacity: 0;
  transition: transform 0.8s cubic-bezier(0.17, 0.89, 0.32, 1.25), opacity 0.8s ease;
}

.draw-cards-container.cards-visible {
  transform: translateY(0);
  opacity: 1;
}

.draw-result-title {
  color: #00aeff;
  font-size: 1.8rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(0,0,0,0.8);
  margin-bottom: 30px;
}

.draw-cards-row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 18px;
  margin-bottom: 30px;
}

.draw-close-btn {
  background: #00aeff;
  color: #fff;
  border: none;
  padding: 12px 36px;
  border-radius: 8px;
  font-weight: 600;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background 0.2s;
}

.draw-close-btn:hover {
  background: #0095cc;
}

@media (max-width: 600px) {
  .draw-cards-row {
    gap: 10px;
  }
  
  .draw-result-title {
    font-size: 1.4rem;
    margin-bottom: 20px;
  }
}
</style>